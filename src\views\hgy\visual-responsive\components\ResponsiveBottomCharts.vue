<template>
  <div class="responsive-bottom-charts">
    <div class="charts-grid">
      <!-- 成交额排名图表 -->
      <div class="chart-card">
        <div class="chart-content">
          <h4 class="chart-title">成交额排名</h4>
          <div class="chart-wrapper" ref="dealAmountChartRef"></div>
        </div>
      </div>

      <!-- 标的溢价趋势图表 -->
      <div class="chart-card">
        <div class="chart-content">
          <h4 class="chart-title">标的溢价趋势</h4>
          <div class="chart-wrapper" ref="targetTrendChartRef"></div>
        </div>
      </div>

      <!-- 资产处置溢价趋势图表 -->
      <div class="chart-card">
        <div class="chart-content">
          <h4 class="chart-title">资产处置溢价趋势</h4>
          <div class="chart-wrapper" ref="assetTrendChartRef"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted, nextTick, inject } from 'vue';
  import * as echarts from 'echarts';

  // 注入响应式数据
  const screenSize = inject('screenSize') as any;
  const breakpoint = inject('breakpoint') as any;

  // 图表引用
  const dealAmountChartRef = ref<HTMLElement>();
  const targetTrendChartRef = ref<HTMLElement>();
  const assetTrendChartRef = ref<HTMLElement>();

  // 图表实例
  let dealAmountChart: echarts.ECharts | null = null;
  let targetTrendChart: echarts.ECharts | null = null;
  let assetTrendChart: echarts.ECharts | null = null;

  // 响应式调整图表大小
  const resizeCharts = () => {
    dealAmountChart?.resize();
    targetTrendChart?.resize();
    assetTrendChart?.resize();
  };

  // 防抖函数
  const debounce = (func: Function, wait: number) => {
    let timeout: NodeJS.Timeout;
    return (...args: any[]) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(null, args), wait);
    };
  };

  const debouncedResize = debounce(resizeCharts, 150);

  // 获取响应式配置
  const getResponsiveConfig = () => {
    const isMobile = screenSize?.width < 768;
    const isTablet = screenSize?.width < 1024;

    return {
      fontSize: {
        title: isMobile ? 12 : isTablet ? 14 : 16,
        axis: isMobile ? 10 : isTablet ? 11 : 12,
        legend: isMobile ? 10 : isTablet ? 11 : 12,
      },
      grid: {
        left: isMobile ? '15%' : '10%',
        right: isMobile ? '15%' : '10%',
        top: isMobile ? '20%' : '15%',
        bottom: isMobile ? '20%' : '15%',
      },
      animation: !isMobile, // 移动端禁用动画以提升性能
    };
  };

  // 初始化成交额排名图表
  const initDealAmountChart = () => {
    if (!dealAmountChartRef.value) return;

    dealAmountChart = echarts.init(dealAmountChartRef.value);
    const config = getResponsiveConfig();

    const option = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(10, 14, 39, 0.9)',
        borderColor: '#2BCCFF',
        textStyle: {
          color: '#ffffff',
          fontSize: config.fontSize.axis,
        },
      },
      grid: config.grid,
      xAxis: {
        type: 'category',
        data: ['郑州', '洛阳', '开封', '新乡', '焦作', '安阳'],
        axisLine: {
          lineStyle: { color: '#2BCCFF' },
        },
        axisLabel: {
          color: '#ffffff',
          fontSize: config.fontSize.axis,
        },
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: { color: '#2BCCFF' },
        },
        axisLabel: {
          color: '#ffffff',
          fontSize: config.fontSize.axis,
          formatter: '{value}万',
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(43, 204, 255, 0.2)',
            type: 'dashed',
          },
        },
      },
      series: [
        {
          name: '成交额',
          type: 'bar',
          data: [12560, 9875, 8732, 7654, 6589, 5432],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#2BCCFF' },
              { offset: 1, color: '#1A8FCC' },
            ]),
            shadowColor: 'rgba(43, 204, 255, 0.3)',
            shadowBlur: 10,
          },
          animationDuration: config.animation ? 2000 : 0,
          animationEasing: 'cubicOut',
        },
      ],
    };

    dealAmountChart.setOption(option);
  };

  // 初始化标的溢价趋势图表
  const initTargetTrendChart = () => {
    if (!targetTrendChartRef.value) return;

    targetTrendChart = echarts.init(targetTrendChartRef.value);
    const config = getResponsiveConfig();

    const option = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(10, 14, 39, 0.9)',
        borderColor: '#2BCCFF',
        textStyle: {
          color: '#ffffff',
          fontSize: config.fontSize.axis,
        },
      },
      grid: config.grid,
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月'],
        axisLine: {
          lineStyle: { color: '#2BCCFF' },
        },
        axisLabel: {
          color: '#ffffff',
          fontSize: config.fontSize.axis,
        },
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: { color: '#2BCCFF' },
        },
        axisLabel: {
          color: '#ffffff',
          fontSize: config.fontSize.axis,
          formatter: '{value}%',
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(43, 204, 255, 0.2)',
            type: 'dashed',
          },
        },
      },
      series: [
        {
          name: '溢价率',
          type: 'line',
          data: [25.8, 28.3, 32.1, 29.7, 35.4, 31.2],
          lineStyle: {
            color: '#2BCCFF',
            width: 3,
            shadowColor: 'rgba(43, 204, 255, 0.3)',
            shadowBlur: 10,
          },
          itemStyle: {
            color: '#2BCCFF',
            shadowColor: 'rgba(43, 204, 255, 0.5)',
            shadowBlur: 8,
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(43, 204, 255, 0.3)' },
              { offset: 1, color: 'rgba(43, 204, 255, 0.05)' },
            ]),
          },
          smooth: true,
          animationDuration: config.animation ? 2000 : 0,
          animationEasing: 'cubicOut',
        },
      ],
    };

    targetTrendChart.setOption(option);
  };

  // 初始化资产处置溢价趋势图表
  const initAssetTrendChart = () => {
    if (!assetTrendChartRef.value) return;

    assetTrendChart = echarts.init(assetTrendChartRef.value);
    const config = getResponsiveConfig();

    const option = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(10, 14, 39, 0.9)',
        borderColor: '#2BCCFF',
        textStyle: {
          color: '#ffffff',
          fontSize: config.fontSize.axis,
        },
      },
      grid: config.grid,
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月'],
        axisLine: {
          lineStyle: { color: '#2BCCFF' },
        },
        axisLabel: {
          color: '#ffffff',
          fontSize: config.fontSize.axis,
        },
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: { color: '#2BCCFF' },
        },
        axisLabel: {
          color: '#ffffff',
          fontSize: config.fontSize.axis,
          formatter: '{value}%',
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(43, 204, 255, 0.2)',
            type: 'dashed',
          },
        },
      },
      series: [
        {
          name: '溢价率',
          type: 'line',
          data: [42.5, 38.9, 45.2, 41.8, 48.6, 44.3],
          lineStyle: {
            color: '#FFC107',
            width: 3,
            shadowColor: 'rgba(255, 193, 7, 0.3)',
            shadowBlur: 10,
          },
          itemStyle: {
            color: '#FFC107',
            shadowColor: 'rgba(255, 193, 7, 0.5)',
            shadowBlur: 8,
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(255, 193, 7, 0.3)' },
              { offset: 1, color: 'rgba(255, 193, 7, 0.05)' },
            ]),
          },
          smooth: true,
          animationDuration: config.animation ? 2000 : 0,
          animationEasing: 'cubicOut',
        },
      ],
    };

    assetTrendChart.setOption(option);
  };

  // 初始化所有图表
  const initCharts = async () => {
    await nextTick();
    initDealAmountChart();
    initTargetTrendChart();
    initAssetTrendChart();
  };

  onMounted(() => {
    initCharts();
    window.addEventListener('resize', debouncedResize);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', debouncedResize);
    dealAmountChart?.dispose();
    targetTrendChart?.dispose();
    assetTrendChart?.dispose();
  });
</script>

<style lang="less" scoped>
  @import '../styles/responsive-variables.less';
  @import '../styles/responsive-mixins.less';

  .responsive-bottom-charts {
    width: 100%;
    height: 100%;
    padding: var(--spacing-md);
  }

  .charts-grid {
    .responsive-grid(3, var(--spacing-lg));
    height: 100%;

    .respond-to(xs) {
      .content() {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }
    }

    .respond-to(sm) {
      .content() {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }
    }

    .mobile-first(md) {
      .content() {
        grid-template-columns: repeat(3, 1fr);
      }
    }
  }

  .chart-card {
    .responsive-chart-container();
    transition: all var(--duration-normal) ease;

    &:hover {
      transform: translateY(-4px);
      .glow-effect(#2bccff, 16px, 0.3);

      &::after {
        border-color: var(--primary-color);
      }
    }
  }

  .chart-content {
    position: relative;
    z-index: 3;
    height: 100%;
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;

    .respond-to(xs) {
      .content() {
        padding: var(--spacing-md);
      }
    }
  }

  .chart-title {
    .responsive-font-size(var(--font-lg));
    color: var(--text-light);
    font-weight: bold;
    text-align: center;
    margin: 0 0 var(--spacing-md) 0;
    .text-glow(var(--primary-color), 10px, 0.5);

    .respond-to(xs) {
      .content() {
        margin-bottom: var(--spacing-sm);
      }
    }
  }

  .chart-wrapper {
    flex: 1;
    min-height: 200px;

    .respond-to(xs) {
      .content() {
        min-height: 160px;
      }
    }
  }

  // 响应式动画优化
  @media (prefers-reduced-motion: reduce) {
    .chart-card {
      transition: none;

      &:hover {
        transform: none;
      }
    }
  }

  // 高对比度模式支持
  @media (prefers-contrast: high) {
    .chart-card {
      &::after {
        border-width: 2px;
      }
    }
  }
</style>
