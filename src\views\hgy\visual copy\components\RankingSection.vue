<template>
  <div class="ranking-section">
    <!-- 标的溢价额排名 -->
    <div class="ranking-container">
      <div class="ranking-bg"></div>
      <div class="ranking-content">
        <div class="ranking-title">标的溢价额排名</div>
        <div class="ranking-list" ref="premiumAmountListRef">
          <div v-for="(item, index) in premiumAmountData" :key="index" class="ranking-item" :class="{ 'top-three': index < 3 }">
            <div class="rank-number">{{ index + 1 }}</div>
            <div class="rank-content">
              <div class="rank-name">{{ item.name }}</div>
              <div class="rank-value">{{ item.value }}万</div>
            </div>
            <div class="rank-icon">
              <div class="icon-triangle" :class="`rank-${index + 1}`"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 标的溢价率排名 -->
    <div class="ranking-container">
      <div class="ranking-bg"></div>
      <div class="ranking-content">
        <div class="ranking-title">标的溢价率排名</div>
        <div class="ranking-list" ref="premiumRateListRef">
          <div v-for="(item, index) in premiumRateData" :key="index" class="ranking-item" :class="{ 'top-three': index < 3 }">
            <div class="rank-number">{{ index + 1 }}</div>
            <div class="rank-content">
              <div class="rank-name">{{ item.name }}</div>
              <div class="rank-value">{{ item.value }}%</div>
            </div>
            <div class="rank-icon">
              <div class="icon-triangle" :class="`rank-${index + 1}`"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 资产处置溢价率排名 -->
    <div class="ranking-container">
      <div class="ranking-bg"></div>
      <div class="ranking-content">
        <div class="ranking-title">资产处置溢价率排名</div>
        <div class="ranking-list" ref="assetDisposalListRef">
          <div v-for="(item, index) in assetDisposalData" :key="index" class="ranking-item" :class="{ 'top-three': index < 3 }">
            <div class="rank-number">{{ index + 1 }}</div>
            <div class="rank-content">
              <div class="rank-name">{{ item.name }}</div>
              <div class="rank-value">{{ item.value }}%</div>
            </div>
            <div class="rank-icon">
              <div class="icon-triangle" :class="`rank-${index + 1}`"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted } from 'vue';

  // 排名数据接口
  interface RankingItem {
    name: string;
    value: number;
  }

  const premiumAmountListRef = ref<HTMLElement>();
  const premiumRateListRef = ref<HTMLElement>();
  const assetDisposalListRef = ref<HTMLElement>();

  // 模拟数据
  const premiumAmountData = ref<RankingItem[]>([
    { name: '标的某某某项目A', value: 5969 },
    { name: '标的某某某项目B', value: 4569 },
    { name: '标的某某某项目C', value: 3969 },
    { name: '标的某某某项目D', value: 2969 },
    { name: '标的某某某项目E', value: 1969 },
    { name: '标的某某某项目F', value: 969 },
    { name: '标的某某某项目G', value: 869 },
    { name: '标的某某某项目H', value: 769 },
    { name: '标的某某某项目I', value: 669 },
    { name: '标的某某某项目J', value: 569 },
  ]);

  const premiumRateData = ref<RankingItem[]>([
    { name: '标的某某某项目A', value: 95.5 },
    { name: '标的某某某项目B', value: 92.3 },
    { name: '标的某某某项目C', value: 89.7 },
    { name: '标的某某某项目D', value: 87.2 },
    { name: '标的某某某项目E', value: 85.8 },
    { name: '标的某某某项目F', value: 83.4 },
    { name: '标的某某某项目G', value: 81.9 },
    { name: '标的某某某项目H', value: 79.6 },
    { name: '标的某某某项目I', value: 77.3 },
    { name: '标的某某某项目J', value: 75.1 },
  ]);

  const assetDisposalData = ref<RankingItem[]>([
    { name: '资产处置项目A', value: 88.9 },
    { name: '资产处置项目B', value: 86.5 },
    { name: '资产处置项目C', value: 84.2 },
    { name: '资产处置项目D', value: 82.7 },
    { name: '资产处置项目E', value: 80.3 },
    { name: '资产处置项目F', value: 78.9 },
    { name: '资产处置项目G', value: 76.4 },
    { name: '资产处置项目H', value: 74.8 },
    { name: '资产处置项目I', value: 72.5 },
    { name: '资产处置项目J', value: 70.2 },
  ]);

  // 滚动动画
  let scrollIntervals: NodeJS.Timeout[] = [];

  const startScrollAnimation = (element: HTMLElement) => {
    if (!element) return;

    const scrollHeight = element.scrollHeight;
    const clientHeight = element.clientHeight;

    if (scrollHeight <= clientHeight) return;

    let scrollTop = 0;
    const scrollStep = 1;
    const scrollDelay = 50;

    const interval = setInterval(() => {
      scrollTop += scrollStep;
      element.scrollTop = scrollTop;

      if (scrollTop >= scrollHeight - clientHeight) {
        // 滚动到底部后，暂停一下再重新开始
        setTimeout(() => {
          scrollTop = 0;
          element.scrollTop = 0;
        }, 2000);
      }
    }, scrollDelay);

    scrollIntervals.push(interval);
  };

  onMounted(() => {
    // 延迟启动滚动动画
    setTimeout(() => {
      if (premiumAmountListRef.value) {
        startScrollAnimation(premiumAmountListRef.value);
      }
      if (premiumRateListRef.value) {
        startScrollAnimation(premiumRateListRef.value);
      }
      if (assetDisposalListRef.value) {
        startScrollAnimation(assetDisposalListRef.value);
      }
    }, 3000);
  });

  onUnmounted(() => {
    scrollIntervals.forEach((interval) => clearInterval(interval));
    scrollIntervals = [];
  });
</script>

<style lang="less" scoped>
  .ranking-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%;
  }

  .ranking-container {
    position: relative;
    flex: 1;
    border-radius: 8px;
    overflow: hidden;
    min-height: 250px;
  }

  .ranking-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/right-bg_1754987518249.png') no-repeat center center;
    background-size: cover;
    z-index: 1;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, rgba(18, 230, 219, 0.05) 0%, rgba(18, 230, 219, 0.02) 50%, rgba(18, 230, 219, 0.05) 100%);
      border: 1px solid rgba(18, 230, 219, 0.2);
      border-radius: 8px;
    }
  }

  .ranking-content {
    position: relative;
    height: 100%;
    padding: 20px;
    z-index: 2;
  }

  .ranking-title {
    color: #12e6db;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 16px;
    text-align: center;
    text-shadow: 0 0 10px rgba(18, 230, 219, 0.5);
  }

  .ranking-list {
    height: calc(100% - 50px);
    overflow: hidden;
  }

  .ranking-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;

    &:hover {
      background: rgba(18, 230, 219, 0.1);
    }

    &.top-three {
      .rank-number {
        background: linear-gradient(135deg, #12e6db 0%, #097884 100%);
        color: #004c66;
        font-weight: bold;
      }
    }
  }

  .rank-number {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    color: #004c66;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
  }

  .rank-content {
    flex: 1;
    min-width: 0;
  }

  .rank-name {
    color: #ffffff;
    font-size: 14px;
    font-weight: 400;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 2px;
  }

  .rank-value {
    color: #12e6db;
    font-size: 16px;
    font-weight: bold;
    text-shadow: 0 0 8px rgba(18, 230, 219, 0.5);
  }

  .rank-icon {
    width: 16px;
    height: 16px;
    margin-left: 8px;
    flex-shrink: 0;
  }

  .icon-triangle {
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 12px solid #12e6db;
    filter: drop-shadow(0 0 4px rgba(18, 230, 219, 0.6));

    &.rank-1 {
      border-bottom-color: #ffd700;
      filter: drop-shadow(0 0 4px rgba(255, 215, 0, 0.6));
    }

    &.rank-2 {
      border-bottom-color: #c0c0c0;
      filter: drop-shadow(0 0 4px rgba(192, 192, 192, 0.6));
    }

    &.rank-3 {
      border-bottom-color: #cd7f32;
      filter: drop-shadow(0 0 4px rgba(205, 127, 50, 0.6));
    }
  }
</style>
