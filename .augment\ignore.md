# Augment 索引忽略规则

此文件定义了 Augment 在建立代码索引时应该忽略的文件和目录。

## 忽略的目录和文件

### 构建输出目录
- `jeecg_ui/` - Vue3 项目的打包输出目录
- `dist/` - 通用构建输出目录
- `build/` - 构建输出目录

### 依赖和缓存
- `node_modules/` - Node.js 依赖包
- `.cache/` - 缓存目录
- `.vite/` - Vite 缓存
- `.nuxt/` - Nuxt 缓存
- `.next/` - Next.js 缓存

### 版本控制
- `.git/` - Git 版本控制目录

### 编辑器和IDE
- `.vscode/` - VS Code 配置
- `.idea/` - IntelliJ IDEA 配置
- `*.swp`, `*.swo` - Vim 临时文件

### 日志和临时文件
- `*.log` - 日志文件
- `logs/` - 日志目录
- `*.tmp`, `*.temp` - 临时文件

### 系统文件
- `.DS_Store` - macOS 系统文件
- `Thumbs.db` - Windows 系统文件

## 说明

这些规则确保 Augment 的代码索引只包含源代码文件，避免包含构建产物、依赖包和其他非源代码文件，从而提高搜索和上下文检索的准确性。
