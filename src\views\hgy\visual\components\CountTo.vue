<template>
  <span class="count-to">{{ displayValue }}</span>
</template>

<script setup lang="ts">
  import { ref, onMounted, watch } from 'vue';

  interface Props {
    start?: number;
    end: number;
    duration?: number;
    decimals?: number;
    separator?: string;
    prefix?: string;
    suffix?: string;
    autoplay?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    start: 0,
    duration: 2000,
    decimals: 0,
    separator: ',',
    prefix: '',
    suffix: '',
    autoplay: true,
  });

  const displayValue = ref('');
  const currentValue = ref(props.start);

  // 格式化数字
  const formatNumber = (num: number): string => {
    const fixed = num.toFixed(props.decimals);
    const parts = fixed.split('.');

    // 添加千分位分隔符
    if (props.separator) {
      parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, props.separator);
    }

    const result = parts.join('.');
    return props.prefix + result + props.suffix;
  };

  // 缓动函数 - easeOutQuart
  const easeOutQuart = (t: number): number => {
    return 1 - Math.pow(1 - t, 4);
  };

  // 开始动画
  const startAnimation = () => {
    const startTime = Date.now();
    const startValue = props.start;
    const endValue = props.end;
    const duration = props.duration;

    const animate = () => {
      const now = Date.now();
      const elapsed = now - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 使用缓动函数
      const easedProgress = easeOutQuart(progress);

      // 计算当前值
      const current = startValue + (endValue - startValue) * easedProgress;
      currentValue.value = current;
      displayValue.value = formatNumber(current);

      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        // 确保最终值准确
        currentValue.value = endValue;
        displayValue.value = formatNumber(endValue);
      }
    };

    animate();
  };

  // 监听 end 值变化
  watch(
    () => props.end,
    () => {
      if (props.autoplay) {
        startAnimation();
      }
    }
  );

  onMounted(() => {
    // 初始显示起始值
    displayValue.value = formatNumber(props.start);

    if (props.autoplay) {
      // 延迟一点开始动画，让组件完全渲染
      setTimeout(() => {
        startAnimation();
      }, 100);
    }
  });

  // 暴露方法供外部调用
  defineExpose({
    start: startAnimation,
  });
</script>

<style lang="less" scoped>
  @import '../styles/fullScreen/CountTo.less';
</style>
