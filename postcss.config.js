const pxToViewport = require('postcss-px-to-viewport-8-plugin');

// 兼容 Windows/Unix 路径
const visualInclude = [/src[\\\/]views[\\\/]hgy[\\\/]visual[\\\/]?/];
const visualExclude = [/src[\\\/]views[\\\/]hgy[\\\/]visual[\\\/]index\.vue$/];

/* module.exports = {
  plugins: [
    require('autoprefixer'),
    pxToViewport({
      unitToConvert: 'px',
      viewportWidth: 1658,
      viewportHeight: 994,
      unitPrecision: 6,
      propList: ['margin*', 'padding*', 'gap', 'column-gap', 'row-gap', 'font*', 'letter-spacing', 'border-radius', 'height'],
      viewportUnit: 'vw',
      fontViewportUnit: 'vw',
      selectorBlackList: [],
      minPixelValue: 1,
      mediaQuery: false,
      replace: true,
      landscape: false,
      include: visualInclude,
      exclude: visualExclude,
    }),
  ],
}; */
