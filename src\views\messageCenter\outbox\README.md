# 消息发件箱页面

## 接口对接说明

### API接口
- **接口地址**: `/receive/hgyReceive/queryAllSend`
- **请求方式**: POST
- **接口文件**: `src/views/messageCenter/outbox/outbox.api.ts`

### 请求参数
```json
{
  "pageNo": 2,
  "pageSize": 10,
  "sendUserId": "1955149354327138306",
  "keywords": "发"
}
```

#### 参数说明
- `pageNo`: 页码，从1开始
- `pageSize`: 每页数量，默认10
- `sendUserId`: 发送用户ID，自动从用户store中获取
- `keywords`: 搜索关键词，可选

### 响应数据结构
```json
{
  "records": [
    {
      "id": "消息ID",
      "receiver": "接收者",
      "productInfo": "关联产品信息",
      "content": "留言内容",
      "sendStatus": "发送状态",
      "messageStatus": "消息状态",
      "sendTime": "发送时间"
    }
  ],
  "total": 100,
  "size": 10,
  "current": 2,
  "pages": 10
}
```

## 功能特性

### 搜索功能
- **关键词搜索**: 支持按关键词搜索消息内容
- **发送状态筛选**: 发送成功、发送失败、发送中
- **消息状态筛选**: 未读、已读、已回复
- **时间区间筛选**: 支持按时间范围查询

### 表格功能
- **分页显示**: 支持分页查询
- **列宽调整**: 支持拖拽调整列宽
- **操作按钮**: 重发（仅失败消息）、删除功能
- **状态标签**: 不同颜色显示发送状态

### 数据处理
- **自动获取用户ID**: 从用户store中自动获取当前用户ID作为发送者ID
- **错误处理**: 接口调用失败时的错误处理
- **参数转换**: 自动处理分页参数转换

## 技术实现

### 文件结构
```
src/views/messageCenter/outbox/
├── index.vue          # 主页面组件
├── outbox.api.ts      # API接口定义
└── README.md          # 说明文档
```

### 关键代码

#### API调用
```typescript
const fetchData = async (params: any) => {
  const requestParams = {
    pageNo: params.page || 1,
    pageSize: params.pageSize || 10,
    sendUserId: String(userStore.getUserInfo?.id || ''),
    keywords: params.keywords || '',
  };

  const result = await queryAllSend(requestParams);
  return {
    items: result.records || [],
    total: result.total || 0,
  };
};
```

#### 发送用户ID获取
```typescript
import { useUserStore } from '/@/store/modules/user';
const userStore = useUserStore();
```

#### 状态显示
```typescript
// 获取状态颜色
function getStatusColor(status: string) {
  const colorMap = {
    success: 'green',
    failed: 'red',
    sending: 'blue',
  };
  return colorMap[status] || 'default';
}

// 获取状态文本
function getStatusText(status: string) {
  const textMap = {
    success: '发送成功',
    failed: '发送失败',
    sending: '发送中',
  };
  return textMap[status] || status;
}
```

## 操作功能

### 重发功能
- **触发条件**: 仅对发送失败的消息显示重发按钮
- **操作流程**: 点击重发 → 调用重发接口 → 更新状态 → 刷新列表

### 删除功能
- **确认机制**: 删除前弹出确认对话框
- **操作流程**: 点击删除 → 确认删除 → 调用删除接口 → 刷新列表

## 注意事项

1. **用户ID**: 接口需要当前登录用户的ID作为发送者ID，确保用户已登录
2. **权限控制**: 只能查看当前用户发送的消息
3. **状态管理**: 根据发送状态动态显示操作按钮
4. **错误处理**: 接口调用失败时的友好提示

## 待开发功能

- [ ] 重发留言的后端接口对接
- [ ] 删除留言的后端接口对接
- [ ] 消息状态实时更新
- [ ] 批量操作功能
- [ ] 消息详情查看功能
