<template>
  <div class="asset-ranking">
    <!-- 背景 -->
    <div class="ranking-bg"></div>

    <!-- 内容 -->
    <div class="ranking-content">
      <!-- 总标题 -->
      <div class="main-title">资产处置数据排名</div>

      <!-- 资产处置溢价额排名 -->
      <div class="ranking-section">
        <div class="section-header">
          <div class="section-divider left"></div>
          <div class="section-title">资产处置溢价额排名</div>
          <div class="section-divider right"></div>
        </div>
        <div class="ranking-list">
          <div v-for="(item, index) in assetPremiumAmountData" :key="index" class="ranking-item">
            <!-- 奖牌图标 -->
            <div class="medal-icon" :class="`rank-${index + 1}`">
              <div class="medal-bg"></div>
              <span class="rank-number">{{ index + 1 }}</span>
            </div>

            <!-- 项目名称 -->
            <div class="project-name">{{ item.name }}</div>

            <!-- 进度条 -->
            <div class="progress-container">
              <div class="progress-bar">
                <div
                  class="progress-fill"
                  :style="{
                    width: animatedAssetPremiumAmountData[index] ? `${(item.value / maxAssetPremiumAmount) * 100}%` : '0%',
                    transitionDelay: `${index * 200}ms`,
                  }"
                >
                  <div class="progress-diamond"></div>
                </div>
              </div>
              <div class="progress-value">
                <span class="animated-number">{{ animatedAssetPremiumAmountValues[index] || 0 }}</span>
                <span class="progress-unit">万</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 资产处置溢价率排名 -->
      <div class="ranking-section">
        <div class="section-header width-limit">
          <div class="section-divider left"></div>
          <div class="section-title">资产处置溢价率排名</div>
          <div class="section-divider right"></div>
        </div>
        <div class="ranking-list">
          <div v-for="(item, index) in assetPremiumRateData" :key="index" class="ranking-item">
            <!-- 奖牌图标 -->
            <div class="medal-icon" :class="`rank-${index + 1}`">
              <div class="medal-bg"></div>
              <span class="rank-number">{{ index + 1 }}</span>
            </div>

            <!-- 项目名称 -->
            <div class="project-name">{{ item.name }}</div>

            <!-- 进度条 -->
            <div class="progress-container">
              <div class="progress-bar">
                <div
                  class="progress-fill"
                  :style="{
                    width: animatedAssetPremiumRateData[index] ? `${item.value}%` : '0%',
                    transitionDelay: `${index * 200}ms`,
                  }"
                >
                  <div class="progress-diamond"></div>
                </div>
              </div>
              <div class="progress-value">
                <span class="animated-number">{{ animatedAssetPremiumRateValues[index] || 0 }}</span>
                <span class="progress-unit">%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue';

  // 模拟数据
  const assetPremiumAmountData = ref([
    { name: '资产处置项目A', value: 5969 },
    { name: '资产处置项目B', value: 4569 },
    { name: '资产处置项目C', value: 3969 },
    { name: '资产处置项目D', value: 2969 },
    { name: '资产处置项目E', value: 1969 },
    { name: '资产处置项目F', value: 969 },
    { name: '资产处置项目G', value: 869 },
  ]);

  const assetPremiumRateData = ref([
    { name: '资产处置项目A', value: 95.5 },
    { name: '资产处置项目B', value: 92.3 },
    { name: '资产处置项目C', value: 89.7 },
    { name: '资产处置项目D', value: 87.2 },
    { name: '资产处置项目E', value: 85.8 },
    { name: '资产处置项目F', value: 83.4 },
    { name: '资产处置项目G', value: 81.9 },
  ]);

  // 动画控制状态
  const animatedAssetPremiumAmountData = ref<boolean[]>(new Array(assetPremiumAmountData.value.length).fill(false));
  const animatedAssetPremiumRateData = ref<boolean[]>(new Array(assetPremiumRateData.value.length).fill(false));

  // 动画数值
  const animatedAssetPremiumAmountValues = ref<number[]>(new Array(assetPremiumAmountData.value.length).fill(0));
  const animatedAssetPremiumRateValues = ref<number[]>(new Array(assetPremiumRateData.value.length).fill(0));

  // 计算最大溢价额用于进度条比例
  const maxAssetPremiumAmount = computed(() => {
    return Math.max(...assetPremiumAmountData.value.map((item) => item.value));
  });

  // 数字动画函数
  const animateNumber = (startValue: number, endValue: number, duration: number, callback: (value: number) => void) => {
    const startTime = Date.now();
    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 使用缓动函数
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const currentValue = startValue + (endValue - startValue) * easeOutQuart;

      callback(Math.round(currentValue * 10) / 10); // 保留一位小数

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };
    requestAnimationFrame(animate);
  };

  // 启动动画
  const startAnimations = () => {
    // 延迟启动，让组件完全渲染
    setTimeout(() => {
      // 启动溢价额动画
      assetPremiumAmountData.value.forEach((item, index) => {
        setTimeout(() => {
          animatedAssetPremiumAmountData.value[index] = true;

          // 启动数字动画
          animateNumber(0, item.value, 2000, (value) => {
            animatedAssetPremiumAmountValues.value[index] = value;
          });
        }, index * 200);
      });

      // 启动溢价率动画
      assetPremiumRateData.value.forEach((item, index) => {
        setTimeout(() => {
          animatedAssetPremiumRateData.value[index] = true;

          // 启动数字动画
          animateNumber(0, item.value, 2000, (value) => {
            animatedAssetPremiumRateValues.value[index] = value;
          });
        }, index * 200);
      });
    }, 500); // 延迟500ms开始动画
  };

  onMounted(() => {
    startAnimations();
  });
</script>

<style lang="less" scoped>
  .asset-ranking {
    position: relative;
    width: 577px;
    height: 578px;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    flex-direction: row-reverse;
  }

  .ranking-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 577px;
    height: 578px;
    background: url('https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/right-bg_1754987518249.png') no-repeat center center;
    background-size: cover;
    z-index: 1;
  }

  .ranking-content {
    position: relative;
    width: 78%;
    height: 100%;
    padding: 20px; /* 由 px->vw 插件转换为相对单位 */
    z-index: 2;
    display: flex;
    flex-direction: column;
    // 靠右显示
  }

  /* 总标题样式 */
  .main-title {
    color: #ffffff;
    font-size: 20px;
    font-family: 'PingFang Bold';
    text-align: center;
    position: relative;
    top: -5px;
  }

  .ranking-section {
    flex: 1;
  }

  /* 板块标题容器 */
  .section-header {
    display: flex;
    align-items: center;
    margin: 10px 0;
    gap: 10px;
    width: 106%;
    // 靠左
    margin-left: -5%;
  }

  .width-limit {
    width: 95%;
    margin-left: 6%;
  }

  /* 板块标题两侧分割线 - 从两侧向中间渐变 */
  .section-divider {
    flex: 1;
    height: 1px;

    &.left {
      background: linear-gradient(90deg, rgba(88, 225, 255, 0.6) 0%, rgba(88, 225, 255, 0.2) 70%, transparent 100%);
    }

    &.right {
      background: linear-gradient(90deg, transparent 0%, rgba(88, 225, 255, 0.2) 30%, rgba(88, 225, 255, 0.6) 100%);
    }
  }

  .section-title {
    color: #58e1ff;
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    text-shadow: 0 0 10px rgba(18, 230, 219, 0.5);
    white-space: nowrap;
    flex-shrink: 0;
  }

  .ranking-list {
    display: flex;
    flex-direction: column;
    width: 380px;
    margin-left: auto; /* 右对齐 */
  }

  .ranking-item {
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.3s ease;
    margin: 2px; /* 由 px->vw 插件转换 */

    &:hover {
      // transform: translateX(-5px);
      transform: scale(1.02);
    }
  }

  .medal-icon {
    position: relative;
    width: 32px;
    height: 20px;
    flex-shrink: 0;

    .medal-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      background-image: url('@/assets/visual/medal-regular.png');
    }

    .rank-number {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #004c66 !important; /* 使用!important确保优先级 */
      font-size: 11px;
      z-index: 2;
      font-family: 'DIN Bold';
    }

    &.rank-1 .medal-bg {
      background-image: url('@/assets/visual/medal-gold.png');
    }

    &.rank-2 .medal-bg {
      background-image: url('@/assets/visual/medal-silver.png');
    }

    &.rank-3 .medal-bg {
      background-image: url('@/assets/visual/medal-cuprum.png');
    }
  }

  .project-name {
    color: #ffffff;
    font-size: 14px; /* 字体将被插件转成 vw */
    width: 120px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .progress-container {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .progress-bar {
    flex: 1;
    height: 8px;
    background: #0b6d73;
    border-radius: 4px;
    overflow: visible; /* 改为visible让菱形角能够显示 */
    position: relative;
  }

  .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #044d60 0%, #58e1ff 100%);
    border-radius: 4px;
    transition: width 2s ease;
    position: relative;
  }

  .progress-diamond {
    position: absolute;
    top: 50%;
    right: -4.95px; /* 菱形宽度的一半，让菱形中心对齐进度条右端 */
    width: 9.9px;
    height: 9.9px;
    background: #eafffe;
    transform: translateY(-50%) rotate(45deg); /* 垂直居中并旋转45度形成菱形 */
    border-radius: 2px; /* 稍微圆润的角 */
  }

  .progress-value {
    color: #fff;
    font-size: 16px;
    font-family: 'DIN Bold';
    min-width: 60px;
    text-align: right;
    text-shadow: 0 0 8px rgba(18, 230, 219, 0.5);

    .animated-number {
      display: inline-block;
      transition: all 0.3s ease;
    }

    .progress-unit {
      font-family: 'PingFang Medium';
      font-size: 12px;
      margin-left: 5px;
    }
  }
</style>
