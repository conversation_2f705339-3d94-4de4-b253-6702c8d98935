import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '显示名称',
    align:"center",
    dataIndex: 'constantName'
   },
   {
    title: '常量值',
    align:"center",
    dataIndex: 'constantValue'
   },
   {
    title: '排序序号',
    align:"center",
    dataIndex: 'sort'
   },
   {
    title: '备注说明',
    align:"center",
    dataIndex: 'remarks'
   },
   {
    title: '是否启用(1:启用 0:禁用)',
    align:"center",
    dataIndex: 'status'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '显示名称',
    field: 'constantName',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入显示名称!'},
          ];
     },
  },
  {
    label: '常量值',
    field: 'constantValue',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入常量值!'},
          ];
     },
  },
  {
    label: '排序序号',
    field: 'sort',
    component: 'InputNumber',
  },
  {
    label: '备注说明',
    field: 'remarks',
    component: 'Input',
  },
  {
    label: '是否启用(1:启用 0:禁用)',
    field: 'status',
    component: 'InputNumber',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  constantName: {title: '显示名称',order: 0,view: 'text', type: 'string',},
  constantValue: {title: '常量值',order: 1,view: 'text', type: 'string',},
  sort: {title: '排序序号',order: 2,view: 'number', type: 'number',},
  remarks: {title: '备注说明',order: 3,view: 'text', type: 'string',},
  status: {title: '是否启用(1:启用 0:禁用)',order: 4,view: 'number', type: 'number',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}