import { defHttp } from '/@/utils/http/axios';
import type { AddAuctionItemTempParams, LegacyAddAuctionItemTempParams, AuctionItemInfo, PageParams, PageResult } from './types';

// 企业信息接口
export interface CompanyInfo {
  color: string | null;
  text: string;
  jsonObject: any;
  label: string;
  title: string;
  value: string;
}

/**
 * 拍卖相关API接口
 */
enum Api {
  /**
   * 增值委托
   */
  // 发布竞价委托
  ADD_AUCTION_ITEM_TEMP = '/hgy/auction/hgyAuctionItemTemp/addAuctionItemTemp',
  // 发布资产处置
  ADD_ASSET_ENTRUST = '/hgy/entrustService/hgyAssetEntrust/addDraft',
  // 发布采购信息
  ADD_PROCUREMENT = '/hgy/entrustService/hgyProcurement/addDraft',
  /**
   * 自主委托
   */
  // 发布竞价委托
  ADD_AUCTION_SELF_ITEM_TEMP = '/hgy/auction/hgyAuction/addAuctionItem',
  // 发布资产处置
  ADD_ASSET_SELF_ENTRUST = '/hgy/entrustService/hgyAssetEntrust/addOfficial',
  // 发布采购信息
  ADD_SELF_PROCUREMENT = '/hgy/entrustService/hgyProcurement/addOfficial',
  // 获取拍卖列表
  GET_AUCTION_LIST = '/hgy/auction/hgyAuctionItemTemp/list',
  // 获取拍卖详情
  GET_AUCTION_DETAIL = '/hgy/auction/hgyAuctionItemTemp/queryById',
  // 更新拍卖信息
  UPDATE_AUCTION_ITEM = '/hgy/auction/hgyAuctionItemTemp/edit',
  // 删除拍卖信息
  DELETE_AUCTION_ITEM = '/hgy/auction/hgyAuctionItemTemp/delete',
}

/* --------------------------------增值委托-------------------------------- */
/**
 * 发布竞价委托接口（新版本 - 增值委托）
 * @param params 委托信息参数
 * @returns Promise<any> 返回发布结果
 */
export const addAuctionItemTemp = (params: AddAuctionItemTempParams) => {
  return defHttp.post({
    url: Api.ADD_AUCTION_ITEM_TEMP,
    params,
  });
};

/**
 * 发布竞价委托接口（兼容旧版本）
 * @param params 委托信息参数
 * @returns Promise<any> 返回发布结果
 */
export const addAuctionItemTempLegacy = (params: LegacyAddAuctionItemTempParams) => {
  return defHttp.post({
    url: Api.ADD_AUCTION_ITEM_TEMP,
    params,
  });
};

/**
 * 发布资产处置接口
 * @param params 资产处置参数
 * @returns Promise<any> 返回发布结果
 */
export const addAssetEntrust = (params: any) => {
  return defHttp.post({
    url: Api.ADD_ASSET_ENTRUST,
    params,
  });
};

/**
 * 发布采购信息接口
 * @param params 采购信息参数
 * @returns Promise<any> 返回发布结果
 */
export const addProcurement = (params: any) => {
  return defHttp.post({
    url: Api.ADD_PROCUREMENT,
    params,
  });
};

/* --------------------------------自主委托-------------------------------- */
/**
 * 发布竞价委托接口（新版本 - 自主委托）
 * @param params 委托信息参数
 * @returns Promise<any> 返回发布结果
 */
export const addAuctionSelfItemTemp = (params: AddAuctionItemTempParams) => {
  return defHttp.post({
    url: Api.ADD_AUCTION_SELF_ITEM_TEMP,
    params,
  });
};

/**
 * 发布资产处置接口（自主委托）
 * @param params 资产处置参数
 * @returns Promise<any> 返回发布结果
 */
export const addSelfAssetEntrust = (params: any) => {
  return defHttp.post({
    url: Api.ADD_ASSET_SELF_ENTRUST,
    params,
  });
};

/**
 * 发布采购信息接口（自主委托）
 * @param params 采购信息参数
 * @returns Promise<any> 返回发布结果
 */
export const addSelfProcurement = (params: any) => {
  return defHttp.post({
    url: Api.ADD_SELF_PROCUREMENT,
    params,
  });
};

/**
 * 获取拍卖列表
 * @param params 查询参数
 * @returns Promise<PageResult<AuctionItemInfo>> 返回分页的拍卖列表
 */
export const getAuctionList = (params?: PageParams) => {
  return defHttp.get<PageResult<AuctionItemInfo>>({
    url: Api.GET_AUCTION_LIST,
    params,
  });
};

/**
 * 获取拍卖详情
 * @param id 拍卖ID
 * @returns Promise<AuctionItemInfo> 返回拍卖详情
 */
export const getAuctionDetail = (id: string) => {
  return defHttp.get<AuctionItemInfo>({
    url: Api.GET_AUCTION_DETAIL,
    params: { id },
  });
};

// 企业信息API响应接口
export interface CompanyListResponse {
  success: boolean;
  message: string;
  code: number;
  result: CompanyInfo[];
  timestamp: number;
}

/**
 * 获取企业信息列表
 * @returns 企业信息列表
 */
export const getCompanyList = () => {
  return defHttp.get<CompanyListResponse>({
    url: '/sys/dict/customGetDictItems/sys_tenant,name,id',
  });
};

/**
 * 更新拍卖信息
 * @param params 更新参数
 * @returns Promise<any> 返回更新结果
 */
export const updateAuctionItem = (params: Partial<AddAuctionItemTempParams> & { id: string }) => {
  return defHttp.put({
    url: Api.UPDATE_AUCTION_ITEM,
    params,
  });
};

/**
 * 删除拍卖信息
 * @param id 拍卖ID
 * @returns Promise<any> 返回删除结果
 */
export const deleteAuctionItem = (id: string) => {
  return defHttp.delete({
    url: Api.DELETE_AUCTION_ITEM,
    params: { id },
  });
};
