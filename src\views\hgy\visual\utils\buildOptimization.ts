/**
 * 构建时图片优化配置
 * 用于Vite构建过程中的图片优化
 */

/**
 * 图片优化配置
 */
export const imageOptimizationConfig = {
  // 图片压缩配置
  compression: {
    // JPEG质量
    jpeg: {
      quality: 85,
      progressive: true,
    },
    // PNG优化
    png: {
      quality: [0.8, 0.9],
      speed: 4,
    },
    // WebP配置
    webp: {
      quality: 80,
      lossless: false,
    },
    // AVIF配置（现代浏览器）
    avif: {
      quality: 75,
      speed: 4,
    },
  },

  // 响应式图片配置
  responsive: {
    // 生成不同尺寸的图片
    sizes: [
      { width: 480, suffix: '-sm' },
      { width: 768, suffix: '-md' },
      { width: 1024, suffix: '-lg' },
      { width: 1920, suffix: '-xl' },
    ],
    // 生成不同格式
    formats: ['webp', 'avif', 'jpeg'],
  },

  // 图片懒加载配置
  lazyLoading: {
    // 占位符配置
    placeholder: {
      blur: 10,
      quality: 20,
      width: 64,
      height: 64,
    },
    // 预加载距离
    rootMargin: '50px',
    // 加载阈值
    threshold: 0.1,
  },
};

/**
 * 生成响应式图片的srcset
 */
export function generateSrcSet(
  imagePath: string,
  sizes: Array<{ width: number; suffix: string }> = imageOptimizationConfig.responsive.sizes
): string {
  return sizes
    .map(({ width, suffix }) => {
      const ext = imagePath.split('.').pop();
      const basePath = imagePath.replace(`.${ext}`, '');
      return `${basePath}${suffix}.${ext} ${width}w`;
    })
    .join(', ');
}

/**
 * 生成picture元素的sources
 */
export function generatePictureSources(
  imagePath: string,
  formats: string[] = imageOptimizationConfig.responsive.formats,
  sizes: Array<{ width: number; suffix: string }> = imageOptimizationConfig.responsive.sizes
): Array<{ type: string; srcset: string }> {
  return formats.map((format) => {
    const srcset = sizes
      .map(({ width, suffix }) => {
        const basePath = imagePath.replace(/\.[^.]+$/, '');
        return `${basePath}${suffix}.${format} ${width}w`;
      })
      .join(', ');

    return {
      type: `image/${format}`,
      srcset,
    };
  });
}

/**
 * 创建优化的图片元素
 */
export function createOptimizedImage(
  src: string,
  alt: string,
  options?: {
    lazy?: boolean;
    responsive?: boolean;
    placeholder?: string;
    className?: string;
    sizes?: string;
  }
): HTMLElement {
  const {
    lazy = true,
    responsive = true,
    placeholder,
    className = '',
    sizes = '(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw',
  } = options || {};

  if (responsive) {
    // 创建picture元素
    const picture = document.createElement('picture');
    picture.className = className;

    // 添加不同格式的source
    const sources = generatePictureSources(src);
    sources.forEach(({ type, srcset }) => {
      const source = document.createElement('source');
      source.type = type;
      source.srcset = srcset;
      source.sizes = sizes;
      picture.appendChild(source);
    });

    // 添加fallback img
    const img = document.createElement('img');
    img.src = placeholder || src;
    img.alt = alt;
    img.sizes = sizes;
    img.srcset = generateSrcSet(src);

    if (lazy) {
      img.loading = 'lazy';
      img.decoding = 'async';
    }

    picture.appendChild(img);
    return picture;
  } else {
    // 创建普通img元素
    const img = document.createElement('img');
    img.src = placeholder || src;
    img.alt = alt;
    img.className = className;

    if (lazy) {
      img.loading = 'lazy';
      img.decoding = 'async';
    }

    return img;
  }
}

/**
 * 图片预加载优先级管理
 */
export class ImagePriorityManager {
  private highPriorityImages: Set<string> = new Set();
  private normalPriorityImages: Set<string> = new Set();
  private lowPriorityImages: Set<string> = new Set();

  /**
   * 设置图片优先级
   */
  setPriority(src: string, priority: 'high' | 'normal' | 'low'): void {
    // 从其他优先级中移除
    this.highPriorityImages.delete(src);
    this.normalPriorityImages.delete(src);
    this.lowPriorityImages.delete(src);

    // 添加到对应优先级
    switch (priority) {
      case 'high':
        this.highPriorityImages.add(src);
        break;
      case 'normal':
        this.normalPriorityImages.add(src);
        break;
      case 'low':
        this.lowPriorityImages.add(src);
        break;
    }
  }

  /**
   * 获取按优先级排序的图片列表
   */
  getSortedImages(): string[] {
    return [...Array.from(this.highPriorityImages), ...Array.from(this.normalPriorityImages), ...Array.from(this.lowPriorityImages)];
  }

  /**
   * 获取指定优先级的图片
   */
  getImagesByPriority(priority: 'high' | 'normal' | 'low'): string[] {
    switch (priority) {
      case 'high':
        return Array.from(this.highPriorityImages);
      case 'normal':
        return Array.from(this.normalPriorityImages);
      case 'low':
        return Array.from(this.lowPriorityImages);
      default:
        return [];
    }
  }

  /**
   * 清除所有优先级设置
   */
  clear(): void {
    this.highPriorityImages.clear();
    this.normalPriorityImages.clear();
    this.lowPriorityImages.clear();
  }
}

/**
 * 图片加载性能监控
 */
export class ImageLoadingMonitor {
  private loadingTimes: Map<string, number> = new Map();
  private loadedImages: Set<string> = new Set();
  private failedImages: Set<string> = new Set();

  /**
   * 开始监控图片加载
   */
  startMonitoring(src: string): void {
    this.loadingTimes.set(src, performance.now());
  }

  /**
   * 记录图片加载成功
   */
  recordSuccess(src: string): number {
    const startTime = this.loadingTimes.get(src);
    if (startTime) {
      const loadTime = performance.now() - startTime;
      this.loadedImages.add(src);
      this.loadingTimes.delete(src);
      return loadTime;
    }
    return 0;
  }

  /**
   * 记录图片加载失败
   */
  recordFailure(src: string): void {
    this.failedImages.add(src);
    this.loadingTimes.delete(src);
  }

  /**
   * 获取加载统计
   */
  getStats(): {
    loading: number;
    loaded: number;
    failed: number;
    successRate: number;
  } {
    const loading = this.loadingTimes.size;
    const loaded = this.loadedImages.size;
    const failed = this.failedImages.size;
    const total = loaded + failed;
    const successRate = total > 0 ? (loaded / total) * 100 : 0;

    return {
      loading,
      loaded,
      failed,
      successRate,
    };
  }

  /**
   * 清除监控数据
   */
  clear(): void {
    this.loadingTimes.clear();
    this.loadedImages.clear();
    this.failedImages.clear();
  }
}

// 导出单例实例
export const imagePriorityManager = new ImagePriorityManager();
export const imageLoadingMonitor = new ImageLoadingMonitor();

/**
 * 数据大屏图片优化建议
 */
export const visualScreenOptimizations = {
  // 关键背景图片
  critical: [
    'https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/visual-bg_1755478111715.png',
    'https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/header_1754987780270.png',
    'https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/center-bg_1754987648914.png',
  ],

  // 次要背景图片
  secondary: [
    'https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/left-bg_1754987425208.png',
    'https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/right-bg_1754987518249.png',
    'https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/bottom-bg_1754987599965.png',
  ],

  // 小图标和装饰图片
  decorative: [
    '/src/assets/visual/head-card.png',
    '/src/assets/visual/address-card.png',
    '/src/assets/visual/center/icon1.png',
    '/src/assets/visual/center/icon2.png',
    '/src/assets/visual/center/icon3.png',
    '/src/assets/visual/center/icon4.png',
    '/src/assets/visual/center/icon5.png',
    '/src/assets/visual/medal-gold.png',
    '/src/assets/visual/medal-silver.png',
    '/src/assets/visual/medal-cuprum.png',
  ],

  // 优化建议
  recommendations: {
    // 关键图片：高质量，立即加载
    critical: {
      quality: 0.9,
      maxWidth: 1920,
      maxHeight: 1080,
      priority: 'high' as const,
      preload: true,
    },

    // 次要图片：中等质量，延迟加载
    secondary: {
      quality: 0.8,
      maxWidth: 1920,
      maxHeight: 1080,
      priority: 'normal' as const,
      preload: false,
    },

    // 装饰图片：较低质量，懒加载
    decorative: {
      quality: 0.7,
      maxWidth: 512,
      maxHeight: 512,
      priority: 'low' as const,
      preload: false,
    },
  },
};
