<template>
  <div class="responsive-main-kpi">
    <div class="kpi-grid">
      <!-- 成交总额 -->
      <div class="kpi-card primary-card">
        <div class="card-content">
          <div class="kpi-icon">
            <DollarOutlined />
          </div>
          <div class="kpi-info">
            <div class="kpi-label">成交总额</div>
            <div class="kpi-value">
              <CountTo 
                :start-val="0" 
                :end-val="kpiData.totalAmount" 
                :duration="2000"
                :decimals="2"
                suffix="万元"
              />
            </div>
            <div class="kpi-trend" :class="{ positive: kpiData.totalAmountTrend > 0 }">
              <CaretUpOutlined v-if="kpiData.totalAmountTrend > 0" />
              <CaretDownOutlined v-else />
              <span>{{ Math.abs(kpiData.totalAmountTrend) }}%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 溢价总额 -->
      <div class="kpi-card secondary-card">
        <div class="card-content">
          <div class="kpi-icon">
            <RiseOutlined />
          </div>
          <div class="kpi-info">
            <div class="kpi-label">溢价总额</div>
            <div class="kpi-value">
              <CountTo 
                :start-val="0" 
                :end-val="kpiData.premiumAmount" 
                :duration="2000"
                :decimals="2"
                suffix="万元"
              />
            </div>
            <div class="kpi-trend" :class="{ positive: kpiData.premiumAmountTrend > 0 }">
              <CaretUpOutlined v-if="kpiData.premiumAmountTrend > 0" />
              <CaretDownOutlined v-else />
              <span>{{ Math.abs(kpiData.premiumAmountTrend) }}%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 总溢价率 -->
      <div class="kpi-card accent-card">
        <div class="card-content">
          <div class="kpi-icon">
            <PercentageOutlined />
          </div>
          <div class="kpi-info">
            <div class="kpi-label">总溢价率</div>
            <div class="kpi-value">
              <CountTo 
                :start-val="0" 
                :end-val="kpiData.premiumRate" 
                :duration="2000"
                :decimals="2"
                suffix="%"
              />
            </div>
            <div class="kpi-trend" :class="{ positive: kpiData.premiumRateTrend > 0 }">
              <CaretUpOutlined v-if="kpiData.premiumRateTrend > 0" />
              <CaretDownOutlined v-else />
              <span>{{ Math.abs(kpiData.premiumRateTrend) }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, inject } from 'vue';
import { 
  DollarOutlined, 
  RiseOutlined, 
  PercentageOutlined,
  CaretUpOutlined,
  CaretDownOutlined 
} from '@ant-design/icons-vue';
import CountTo from './CountTo.vue';

// 注入数据服务
const dataService = inject('dataService') as any;

// KPI数据
const kpiData = ref({
  totalAmount: 0,
  totalAmountTrend: 0,
  premiumAmount: 0,
  premiumAmountTrend: 0,
  premiumRate: 0,
  premiumRateTrend: 0,
});

// 加载数据
const loadData = async () => {
  try {
    // 模拟数据加载
    await new Promise(resolve => setTimeout(resolve, 500));
    
    kpiData.value = {
      totalAmount: 12580.56,
      totalAmountTrend: 15.8,
      premiumAmount: 3247.89,
      premiumAmountTrend: 8.5,
      premiumRate: 25.82,
      premiumRateTrend: -2.3,
    };
  } catch (error) {
    console.error('加载KPI数据失败:', error);
  }
};

onMounted(() => {
  loadData();
});
</script>

<style lang="less" scoped>
@import '../styles/responsive-variables.less';
@import '../styles/responsive-mixins.less';

.responsive-main-kpi {
  width: 100%;
  padding: var(--spacing-md);
}

.kpi-grid {
  .responsive-grid(3, var(--spacing-lg));
  
  .respond-to(xs) {
    .content() {
      grid-template-columns: 1fr;
      gap: var(--spacing-md);
    }
  }
  
  .respond-to(sm) {
    .content() {
      grid-template-columns: 1fr;
      gap: var(--spacing-md);
    }
  }
  
  .mobile-first(md) {
    .content() {
      grid-template-columns: repeat(3, 1fr);
    }
  }
}

.kpi-card {
  position: relative;
  height: 120px;
  border-radius: var(--radius-lg);
  overflow: hidden;
  cursor: pointer;
  transition: all var(--duration-normal) ease;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-bg);
    z-index: 1;
  }
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    z-index: 2;
    transition: all var(--duration-normal) ease;
  }
  
  &:hover {
    transform: translateY(-4px);
    .glow-effect(var(--primary-color), 16px, 0.3);
    
    &::after {
      border-color: var(--primary-color);
    }
  }
  
  &.primary-card {
    &::before {
      background: linear-gradient(135deg, var(--primary-color-10) 0%, var(--primary-color-20) 100%);
    }
    
    &:hover {
      .glow-effect(var(--primary-color), 20px, 0.4);
    }
  }
  
  &.secondary-card {
    &::before {
      background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.2) 100%);
    }
    
    &:hover {
      .glow-effect(#ffc107, 20px, 0.4);
    }
  }
  
  &.accent-card {
    &::before {
      background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.2) 100%);
    }
    
    &:hover {
      .glow-effect(#28a745, 20px, 0.4);
    }
  }
  
  .respond-to(xs) {
    .content() {
      height: 100px;
    }
  }
}

.card-content {
  position: relative;
  z-index: 3;
  height: 100%;
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  
  .respond-to(xs) {
    .content() {
      padding: var(--spacing-md);
      gap: var(--spacing-sm);
    }
  }
}

.kpi-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-color-20);
  border: 1px solid var(--primary-color);
  border-radius: 50%;
  color: var(--primary-color);
  font-size: 24px;
  .glow-effect(var(--primary-color), 8px, 0.3);
  
  .secondary-card & {
    background: rgba(255, 193, 7, 0.2);
    border-color: #ffc107;
    color: #ffc107;
    .glow-effect(#ffc107, 8px, 0.3);
  }
  
  .accent-card & {
    background: rgba(40, 167, 69, 0.2);
    border-color: #28a745;
    color: #28a745;
    .glow-effect(#28a745, 8px, 0.3);
  }
  
  .respond-to(xs) {
    .content() {
      width: 40px;
      height: 40px;
      font-size: 20px;
    }
  }
}

.kpi-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.kpi-label {
  .responsive-font-size(var(--font-sm));
  color: var(--text-secondary);
  font-weight: 500;
  
  .respond-to(xs) {
    .content() {
      font-size: var(--font-xs);
    }
  }
}

.kpi-value {
  .responsive-font-size(var(--font-xl));
  color: var(--text-light);
  font-weight: bold;
  .text-glow(var(--primary-color), 8px, 0.4);
  
  .secondary-card & {
    .text-glow(#ffc107, 8px, 0.4);
  }
  
  .accent-card & {
    .text-glow(#28a745, 8px, 0.4);
  }
  
  .respond-to(xs) {
    .content() {
      font-size: var(--font-lg);
    }
  }
}

.kpi-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  .responsive-font-size(var(--font-xs));
  color: #dc3545;
  font-weight: 500;
  
  &.positive {
    color: #28a745;
  }
  
  .anticon {
    font-size: 12px;
  }
}

// 响应式动画优化
@media (prefers-reduced-motion: reduce) {
  .kpi-card {
    transition: none;
    
    &:hover {
      transform: none;
    }
  }
}

// 高对比度模式支持
@media (prefers-contrast: high) {
  .kpi-card {
    &::after {
      border-width: 2px;
    }
  }
  
  .kpi-icon {
    border-width: 2px;
  }
}</style>
