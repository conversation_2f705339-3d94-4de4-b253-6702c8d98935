<template>
  <div class="p-4">
    <BasicTable @register="registerTable">
      <!-- 表格标题工具栏 -->
      <template #tableTitle>
        <a-button type="primary" @click="handleAdd">
          <template #icon>
            <PlusOutlined />
          </template>
          新增
        </a-button>
      </template>

      <!-- 操作栏 -->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>

      <!-- 模板类型列 -->
      <template #templateType="{ text }">
        <a-tag :color="getTemplateTypeColor(text)">
          {{ getTemplateTypeText(text) }}
        </a-tag>
      </template>

      <!-- 是否应用列 -->
      <template #useStatus="{ text }">
        <a-tag :color="text === '1' ? 'green' : 'default'">
          {{ text === '1' ? '是' : '否' }}
        </a-tag>
      </template>

      <!-- 模板内容列 -->
      <template #templateContent="{ text }">
        <div class="template-content-preview">
          <span v-if="text && text.length > 50">{{ text.substring(0, 50) }}...</span>
          <span v-else>{{ text || '-' }}</span>
        </div>
      </template>
    </BasicTable>

    <!-- 新增/编辑弹窗 -->
    <CustomModal
      v-model:open="modalVisible"
      :title="modalTitle"
      width="800px"
      :show-footer="true"
      :show-cancel-button="true"
      :show-confirm-button="!isViewMode"
      cancel-text="取消"
      confirm-text="确认"
      @confirm="handleSubmit"
      @cancel="handleCancel"
    >
      <div class="modal-content">
        <a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical" :disabled="isViewMode">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="模板标题" name="templateName">
                <a-input v-model:value="formData.templateName" placeholder="请输入模板标题" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="模板编码" name="templateCode">
                <a-input v-model:value="formData.templateCode" placeholder="请输入模板编码" :disabled="isUpdate" />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="模板类型" name="templateType">
                <a-select v-model:value="formData.templateType" placeholder="请选择模板类型" @change="handleTemplateTypeChange">
                  <a-select-option value="1">短信模板</a-select-option>
                  <a-select-option value="2">邮件模板</a-select-option>
                  <a-select-option value="3">站内信模板</a-select-option>
                  <a-select-option value="4">微信模板</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="是否应用" name="useStatus">
                <a-switch v-model:checked="useStatusChecked" checked-children="是" un-checked-children="否" />
              </a-form-item>
            </a-col>
          </a-row>

          <a-form-item label="模板内容" name="templateContent">
            <!-- 短信和站内信使用多行文本框 -->
            <a-textarea
              v-if="formData.templateType === '1' || formData.templateType === '3'"
              v-model:value="formData.templateContent"
              placeholder="请输入模板内容"
              :rows="6"
              :maxlength="500"
              show-count
            />
            <!-- 邮件和微信使用富文本编辑器 -->
            <JEditorTiptap
              v-else-if="formData.templateType === '2' || formData.templateType === '4'"
              v-model:value="formData.templateContent"
              height="300px"
              placeholder="请输入模板内容..."
            />
            <!-- 未选择类型时的提示 -->
            <div v-else class="template-type-tip">
              <a-empty description="请先选择模板类型" />
            </div>
          </a-form-item>
        </a-form>
      </div>
    </CustomModal>

    <!-- 发送测试弹窗 -->
    <CustomModal
      v-model:open="sendModalVisible"
      title="发送测试"
      width="600px"
      :show-footer="true"
      :show-cancel-button="true"
      :show-confirm-button="true"
      cancel-text="取消"
      confirm-text="发送"
      @confirm="handleSendTest"
      @cancel="handleSendCancel"
    >
      <div class="send-modal-content">
        <a-form ref="sendFormRef" :model="sendFormData" :rules="sendFormRules" layout="vertical">
          <a-form-item label="模板标题">
            <a-input :value="sendFormData.templateName" disabled />
          </a-form-item>

          <a-form-item label="模板内容">
            <a-textarea :value="sendFormData.templateContent" disabled :rows="5" />
          </a-form-item>

          <a-form-item label="测试数据" name="testData">
            <a-textarea
              v-model:value="sendFormData.testData"
              placeholder='请输入JSON格式测试数据，例如：{"name": "张三", "phone": "13800138000"}'
              :rows="5"
            />
          </a-form-item>

          <a-form-item label="消息类型" name="msgType">
            <a-radio-group v-model:value="sendFormData.msgType">
              <a-radio value="system">系统消息</a-radio>
              <a-radio value="user">用户消息</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-form>
      </div>
    </CustomModal>
  </div>
</template>

<script lang="ts" setup name="MessageTemplate">
  import { ref, reactive, computed } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { ActionItem, BasicColumn } from '/@/components/Table';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { CustomModal } from '/@/components/Modal';
  import JEditorTiptap from '/@/components/Form/src/jeecg/components/JEditorTiptap.vue';
  import { list, saveOrUpdate, deleteBatch, sendMessageTest } from '/@/views/system/message/template/template.api';
  import { PlusOutlined } from '@ant-design/icons-vue';

  const { createMessage } = useMessage();

  // 表格列配置
  const columns: BasicColumn[] = [
    {
      title: '模板标题',
      dataIndex: 'templateName',
      width: 150,
    },
    {
      title: '模板编码',
      dataIndex: 'templateCode',
      width: 150,
    },
    {
      title: '模板类型',
      dataIndex: 'templateType',
      width: 120,
      slots: { customRender: 'templateType' },
    },
    {
      title: '模板内容',
      dataIndex: 'templateContent',
      width: 200,
      slots: { customRender: 'templateContent' },
    },
    {
      title: '是否应用',
      dataIndex: 'useStatus',
      width: 100,
      slots: { customRender: 'useStatus' },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
    },
  ];

  // 表格配置
  const [registerTable, { reload }] = useTable({
    api: list,
    columns,
    striped: false,
    useSearchForm: true,
    showTableSetting: false,
    bordered: false,
    showIndexColumn: false,
    canResize: true,
    inset: true,
    maxHeight: 478,
    actionColumn: {
      width: 280,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      fixed: 'right',
    },
    formConfig: {
      labelWidth: 64,
      size: 'large',
      schemas: [
        {
          field: 'templateName',
          label: '模板标题',
          component: 'Input',
          componentProps: {
            placeholder: '请输入模板标题',
          },
          colProps: { span: 6 },
        },
        {
          field: 'templateCode',
          label: '模板编码',
          component: 'Input',
          componentProps: {
            placeholder: '请输入模板编码',
          },
          colProps: { span: 6 },
        },
        {
          field: 'templateType',
          label: '模板类型',
          component: 'Select',
          componentProps: {
            placeholder: '请选择模板类型',
            options: [
              { label: '短信模板', value: '1' },
              { label: '邮件模板', value: '2' },
              { label: '站内信模板', value: '3' },
              { label: '微信模板', value: '4' },
            ],
          },
          colProps: { span: 6 },
        },
      ],
    },
  });

  // 弹窗相关状态
  const modalVisible = ref(false);
  const modalTitle = ref('');
  const isUpdate = ref(false);
  const isViewMode = ref(false);
  const formRef = ref();

  // 表单数据
  const formData = reactive({
    id: '',
    templateName: '',
    templateCode: '',
    templateType: '',
    templateContent: '',
    useStatus: '0',
  });

  // 是否应用的计算属性
  const useStatusChecked = computed({
    get: () => formData.useStatus === '1',
    set: (val: boolean) => {
      formData.useStatus = val ? '1' : '0';
    },
  });

  // 表单验证规则
  const formRules = {
    templateName: [{ required: true, message: '请输入模板标题', trigger: 'blur' }],
    templateCode: [{ required: true, message: '请输入模板编码', trigger: 'blur' }],
    templateType: [{ required: true, message: '请选择模板类型', trigger: 'change' }],
    templateContent: [{ required: true, message: '请输入模板内容', trigger: 'blur' }],
  };

  // 发送测试弹窗相关状态
  const sendModalVisible = ref(false);
  const sendFormRef = ref();
  const sendFormData = reactive({
    templateCode: '',
    templateName: '',
    templateContent: '',
    testData: '{}',
    msgType: 'system',
  });

  // 发送测试表单验证规则
  const sendFormRules = {
    testData: [{ required: true, message: '请输入测试数据', trigger: 'blur' }],
    msgType: [{ required: true, message: '请选择消息类型', trigger: 'change' }],
  };

  // 获取模板类型颜色
  const getTemplateTypeColor = (type: string) => {
    const colorMap = {
      '1': 'blue', // 短信模板
      '2': 'green', // 邮件模板
      '3': 'orange', // 站内信模板
      '4': 'purple', // 微信模板
    };
    return colorMap[type] || 'default';
  };

  // 获取模板类型文本
  const getTemplateTypeText = (type: string) => {
    const textMap = {
      '1': '短信模板',
      '2': '邮件模板',
      '3': '站内信模板',
      '4': '微信模板',
    };
    return textMap[type] || '未知';
  };

  // 获取表格操作按钮
  const getTableAction = (record: any): ActionItem[] => {
    const actions: ActionItem[] = [
      {
        label: '查看',
        onClick: () => handleView(record),
      },
      {
        label: '编辑',
        onClick: () => handleEdit(record),
        ifShow: record.useStatus !== '1', // 已应用的模板不能编辑
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '确定要删除这个模板吗？',
          placement: 'left',
          confirm: () => handleDelete(record),
        },
        ifShow: record.useStatus !== '1', // 已应用的模板不能删除
      },
      {
        label: record.useStatus === '1' ? '停用' : '应用',
        color: record.useStatus === '1' ? 'warning' : 'success',
        onClick: () => handleToggleStatus(record),
      },
      {
        label: '发送',
        onClick: () => handleSend(record),
      },
    ];
    return actions;
  };

  // 新增
  const handleAdd = () => {
    resetForm();
    modalTitle.value = '新增消息模板';
    isUpdate.value = false;
    isViewMode.value = false;
    modalVisible.value = true;
  };

  // 查看
  const handleView = (record: any) => {
    resetForm();
    Object.assign(formData, record);
    modalTitle.value = '查看消息模板';
    isUpdate.value = true;
    isViewMode.value = true;
    modalVisible.value = true;
  };

  // 编辑
  const handleEdit = (record: any) => {
    if (record.useStatus === '1') {
      createMessage.warning('此模板已被应用，禁止编辑!');
      return;
    }
    resetForm();
    Object.assign(formData, record);
    modalTitle.value = '编辑消息模板';
    isUpdate.value = true;
    isViewMode.value = false;
    modalVisible.value = true;
  };

  // 删除
  const handleDelete = async (record: any) => {
    try {
      await deleteBatch({ ids: record.id }, false);
      createMessage.success('删除成功');
      reload();
    } catch (error) {
      console.error('删除失败:', error);
    }
  };

  // 切换应用状态
  const handleToggleStatus = async (record: any) => {
    try {
      const newStatus = record.useStatus === '1' ? '0' : '1';
      const action = newStatus === '1' ? '应用' : '停用';

      await saveOrUpdate({ id: record.id, useStatus: newStatus }, true);
      createMessage.success(`${action}成功`);
      reload();
    } catch (error) {
      console.error('操作失败:', error);
    }
  };

  // 发送测试
  const handleSend = (record: any) => {
    Object.assign(sendFormData, {
      templateCode: record.templateCode,
      templateName: record.templateName,
      templateContent: record.templateContent,
      testData: '{}',
      msgType: 'system',
    });
    sendModalVisible.value = true;
  };

  // 重置表单
  const resetForm = () => {
    Object.assign(formData, {
      id: '',
      templateName: '',
      templateCode: '',
      templateType: '',
      templateContent: '',
      useStatus: '0',
    });
  };

  // 模板类型变化处理
  const handleTemplateTypeChange = () => {
    // 切换模板类型时清空内容
    formData.templateContent = '';
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      await formRef.value?.validate();

      const params = { ...formData };
      await saveOrUpdate(params, isUpdate.value);

      createMessage.success(isUpdate.value ? '编辑成功' : '新增成功');
      modalVisible.value = false;
      reload();
    } catch (error) {
      console.error('提交失败:', error);
    }
  };

  // 取消弹窗
  const handleCancel = () => {
    modalVisible.value = false;
  };

  // 发送测试消息
  const handleSendTest = async () => {
    try {
      await sendFormRef.value?.validate();

      // 验证JSON格式
      try {
        JSON.parse(sendFormData.testData);
      } catch {
        createMessage.error('测试数据格式错误，请输入有效的JSON格式');
        return;
      }

      await sendMessageTest(sendFormData);
      createMessage.success('测试消息发送成功');
      sendModalVisible.value = false;
    } catch (error) {
      console.error('发送失败:', error);
    }
  };

  // 取消发送测试
  const handleSendCancel = () => {
    sendModalVisible.value = false;
  };
</script>

<style lang="less" scoped>
  .template-content-preview {
    max-width: 200px;
    word-break: break-all;
    line-height: 1.4;
  }

  .template-type-tip {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100px;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    background-color: #fafafa;
  }

  .modal-content {
    padding: 16px 0;
  }

  .send-modal-content {
    padding: 16px 0;
  }

  :deep(.ant-form-item-label) {
    font-weight: 500;
  }

  :deep(.ant-table-tbody > tr > td) {
    vertical-align: top;
  }

  :deep(.ant-tag) {
    margin-right: 0;
  }
</style>
