<template>
  <div class="bottom-charts">
    <!-- 成交额排名 -->
    <div class="chart-card ranking-card">
      <div class="card-bg"></div>
      <div class="card-content">
        <div class="card-title">成交额排名</div>
        <div class="ranking-grid">
          <!-- 前三名 -->
          <div class="top-row">
            <div v-for="(item, index) in topThreeRanking" :key="index" class="ranking-medal" :class="`rank-${index + 1}`">
              <div class="medal-bg-container"></div>
              <div class="medal-content">
                <div class="medal-icon">
                  <div class="medal-bg"></div>
                  <span class="rank-number">{{ index + 1 }}</span>
                </div>
                <div class="medal-info">
                  <div class="medal-value">
                    {{ item.value }}
                    <span class="medal-unit">万</span>
                  </div>
                  <div class="medal-name">{{ item.name }}</div>
                </div>
              </div>
            </div>
          </div>
          <!-- 后三名 -->
          <div class="bottom-row">
            <div v-for="(item, index) in bottomThreeRanking" :key="index" class="ranking-medal" :class="`rank-${index + 4}`">
              <div class="medal-bg-container"></div>
              <div class="medal-content">
                <div class="medal-icon">
                  <div class="medal-bg"></div>
                  <span class="rank-number">{{ index + 4 }}</span>
                </div>
                <div class="medal-info">
                  <div class="medal-value">{{ item.value }} 万</div>
                  <div class="medal-name">{{ item.name }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 标的溢价趋势 -->
    <div class="chart-card trend-card">
      <div class="card-bg"></div>
      <div class="card-content">
        <div class="card-header">
          <div class="card-title">标的溢价趋势</div>
          <div class="card-controls">
            <div class="chart-legend">
              <div class="legend-item">
                <div class="legend-icon line-icon"></div>
                <span class="legend-text">溢价率</span>
              </div>
              <div class="legend-item">
                <div class="legend-icon bar-icon"></div>
                <span class="legend-text">溢价额</span>
              </div>
            </div>
            <div class="chart-filter">
              <select class="filter-select">
                <option value="30">近30天</option>
                <option value="60">近60天</option>
                <option value="90">近90天</option>
              </select>
            </div>
          </div>
        </div>
        <div class="chart-wrapper">
          <div ref="trendChartRef" class="chart"></div>
        </div>
      </div>
    </div>

    <!-- 资产处置溢价趋势 -->
    <div class="chart-card asset-trend-card">
      <div class="card-bg"></div>
      <div class="card-content">
        <div class="card-header">
          <div class="card-title">资产处置溢价趋势</div>
          <div class="card-controls">
            <div class="chart-legend">
              <div class="legend-item">
                <div class="legend-icon line-icon"></div>
                <span class="legend-text">溢价率</span>
              </div>
              <div class="legend-item">
                <div class="legend-icon bar-icon"></div>
                <span class="legend-text">溢价额</span>
              </div>
            </div>
            <div class="chart-filter">
              <select class="filter-select">
                <option value="30">近30天</option>
                <option value="60">近60天</option>
                <option value="90">近90天</option>
              </select>
            </div>
          </div>
        </div>
        <div class="chart-wrapper">
          <div ref="assetTrendChartRef" class="chart"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted, nextTick } from 'vue';
  import * as echarts from 'echarts';

  const trendChartRef = ref<HTMLElement>();
  const assetTrendChartRef = ref<HTMLElement>();
  let trendChart: echarts.ECharts | null = null;
  let assetTrendChart: echarts.ECharts | null = null;

  // 排名数据
  const topThreeRanking = ref([
    { name: '标的某某某项目A', value: 969.26 },
    { name: '标的某某某项目B', value: 969.26 },
    { name: '标的某某某项目C', value: 969.26 },
  ]);

  const bottomThreeRanking = ref([
    { name: '标的某某某项目D', value: 969.26 },
    { name: '标的某某某项目E', value: 969.26 },
    { name: '标的某某某项目F', value: 969.26 },
  ]);

  // 初始化趋势图表
  const initTrendChart = () => {
    if (!trendChartRef.value) return;

    trendChart = echarts.init(trendChartRef.value);

    const option = {
      backgroundColor: 'transparent',
      grid: {
        top: '18%',
        left: '2px',
        right: '2px',
        bottom: '2px',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: ['6.28', '7.01', '7.04', '7.07', '7.10', '7.13', '7.16', '7.19', '7.22', '7.25'],
        axisLine: {
          lineStyle: {
            color: '#2BCCFF',
            width: 1,
          },
        },
        axisLabel: {
          color: '#2BCCFF',
          fontSize: 12,
        },
        axisTick: {
          show: false,
        },
      },
      yAxis: [
        {
          type: 'value',
          name: '万元',
          nameTextStyle: {
            color: '#2BCCFF',
            fontSize: 12,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            color: '#2BCCFF',
            fontSize: 12,
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)',
              type: 'dashed',
            },
          },
        },
        {
          type: 'value',
          name: '%',
          nameTextStyle: {
            color: '#2BCCFF',
            fontSize: 12,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            color: '#2BCCFF',
            fontSize: 12,
          },
        },
      ],
      series: [
        {
          name: '万元',
          type: 'bar',
          yAxisIndex: 0,
          barWidth: 20,
          z: 2,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#2BCCFF' },
              { offset: 1, color: '#044D60' },
            ]),
            borderRadius: [4, 4, 0, 0],
          },
          data: [300, 500, 200, 600, 400, 700, 350, 200, 650, 600],
        },
        {
          name: '%',
          type: 'line',
          yAxisIndex: 1,
          smooth: false,
          symbol: 'circle',
          symbolSize: [8, 8],
          z: 1,
          lineStyle: {
            width: 2,
            color: '#2BCCFF',
          },
          itemStyle: {
            color: '#ffffff',
            borderColor: '#2BCCFF',
            borderWidth: 3,
          },
          areaStyle: {
            color: '#2BCCFF',
            opacity: 0.3,
          },
          data: [40, 80, 60, 90, 70, 50, 80, 30, 100, 95],
        },
      ],
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#2BCCFF',
        borderWidth: 1,
        textStyle: {
          color: '#2BCCFF',
          fontSize: 12,
        },
      },
    };

    trendChart.setOption(option);
  };

  // 初始化资产处置趋势图表
  const initAssetTrendChart = () => {
    if (!assetTrendChartRef.value) return;

    assetTrendChart = echarts.init(assetTrendChartRef.value);

    // 使用相同的配置，但数据略有不同
    const option = {
      backgroundColor: 'transparent',
      grid: {
        top: '18%',
        left: '2px',
        right: '2px',
        bottom: '2px',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: ['6.28', '7.01', '7.04', '7.07', '7.10', '7.13', '7.16', '7.19', '7.22', '7.25'],
        axisLine: {
          lineStyle: {
            color: '#2BCCFF',
            width: 1,
          },
        },
        axisLabel: {
          color: '#2BCCFF',
          fontSize: 12,
        },
        axisTick: {
          show: false,
        },
      },
      yAxis: [
        {
          type: 'value',
          name: '万元',
          nameTextStyle: {
            color: '#2BCCFF',
            fontSize: 12,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            color: '#2BCCFF',
            fontSize: 12,
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)',
              type: 'dashed',
            },
          },
        },
        {
          type: 'value',
          name: '%',
          nameTextStyle: {
            color: '#2BCCFF',
            fontSize: 12,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            color: '#2BCCFF',
            fontSize: 12,
          },
        },
      ],
      series: [
        {
          name: '万元',
          type: 'bar',
          yAxisIndex: 0,
          barWidth: 20,
          z: 2,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#2BCCFF' },
              { offset: 1, color: '#044D60' },
            ]),
            borderRadius: [4, 4, 0, 0],
          },
          data: [250, 450, 180, 550, 380, 650, 320, 180, 600, 550],
        },
        {
          name: '%',
          type: 'line',
          yAxisIndex: 1,
          smooth: false,
          symbol: 'circle',
          symbolSize: [8, 8],
          z: 1,
          lineStyle: {
            width: 2,
            color: '#2BCCFF',
          },
          itemStyle: {
            color: '#ffffff',
            borderColor: '#2BCCFF',
            borderWidth: 3,
          },
          areaStyle: {
            color: '#2BCCFF',
            opacity: 0.3,
          },
          data: [35, 75, 55, 85, 65, 45, 75, 25, 95, 90],
        },
      ],
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#2BCCFF',
        borderWidth: 1,
        textStyle: {
          color: '#2BCCFF',
          fontSize: 12,
        },
      },
    };

    assetTrendChart.setOption(option);
  };

  // 窗口大小变化时重新调整图表
  const handleResize = () => {
    trendChart?.resize();
    assetTrendChart?.resize();
  };

  onMounted(async () => {
    await nextTick();
    initTrendChart();
    initAssetTrendChart();
    window.addEventListener('resize', handleResize);
  });

  onUnmounted(() => {
    trendChart?.dispose();
    assetTrendChart?.dispose();
    window.removeEventListener('resize', handleResize);
  });
</script>

<style lang="less" scoped>
  @import '../styles/fullScreen/BottomCharts.less';
</style>
