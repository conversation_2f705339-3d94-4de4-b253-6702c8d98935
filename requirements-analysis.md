# 板块需求分析文档

## 📋 项目概述

在灰谷网项目中重新实现以下四个核心板块：
1. 发布供求信息板块
2. 我的发布板块  
3. 我的账户板块
4. 消息中心板块

## 🎯 板块详细需求分析

### 1. 发布供求信息板块 (Supply & Demand Info)

#### 1.1 功能概述
用户可以发布供应信息和需求信息，支持物资交易信息的发布和管理。

#### 1.2 核心功能
- **供应信息发布**：用户发布自己拥有的物资供应信息
- **需求信息发布**：用户发布自己需要的物资需求信息
- **分步骤表单**：采用多步骤表单提升用户体验
- **物资分类**：支持物资类型的分类选择
- **地理位置**：支持地址选择和定位功能
- **文件上传**：支持图片和附件上传
- **信息预览**：发布前可预览信息内容

#### 1.3 业务流程
1. 选择发布类型（供应/需求）
2. 填写基本信息（标题、描述、亮点等）
3. 选择物资类型和详细信息
4. 设置价格和交易条件
5. 选择地理位置
6. 上传相关图片和文档
7. 填写联系方式
8. 预览并发布

#### 1.4 数据结构
```typescript
interface SupplyDemandInfo {
  id?: string;
  type: '4' | '5'; // 4-供应, 5-需求
  infoTitle: string; // 信息标题
  highlights?: string; // 供应亮点
  materialType: string; // 物资类型
  materialDesc: string; // 物资描述
  quantity: number; // 数量
  unit: string; // 单位
  price: number; // 价格
  brand?: string; // 品牌
  model?: string; // 型号
  depreciationDegree: number; // 折旧程度
  province: string; // 省份
  city: string; // 城市
  district: string; // 区县
  address: string; // 详细地址
  relationUser: string; // 联系人
  relationPhone: string; // 联系电话
  validType: string; // 有效期类型
  validDate?: string; // 有效期
  servicePayType: '1' | '2'; // 服务付费方式
  attachments?: string[]; // 附件列表
}
```

### 2. 我的发布板块 (My Publish List)

#### 2.1 功能概述
用户查看和管理自己发布的所有供求信息，支持编辑、删除、状态管理等操作。

#### 2.2 核心功能
- **发布列表**：分页显示用户的所有发布信息
- **分类筛选**：按供应/需求类型筛选
- **状态管理**：显示发布状态（草稿、已发布、已过期等）
- **快速操作**：编辑、删除、复制、下架等操作
- **搜索功能**：支持标题、物资类型等关键词搜索
- **批量操作**：支持批量删除、批量下架等
- **数据统计**：显示发布数量、浏览量等统计信息

#### 2.3 页面布局
- **筛选区域**：类型、状态、时间范围筛选
- **列表区域**：卡片式或表格式展示发布信息
- **操作区域**：单个和批量操作按钮
- **分页区域**：分页导航组件

#### 2.4 状态定义
```typescript
enum PublishStatus {
  DRAFT = 1,      // 草稿
  PUBLISHED = 2,  // 已发布
  EXPIRED = 3,    // 已过期
  OFFLINE = 4,    // 已下架
  DELETED = 5     // 已删除
}
```

### 3. 我的账户板块 (My Account)

#### 3.1 功能概述
用户个人账户信息管理中心，包含账户概览、积分管理、库存管理等功能。

#### 3.2 子模块详细需求

##### 3.2.1 账户概览 (Account Overview)
- **基本信息**：用户名、头像、等级、认证状态
- **资产概览**：账户余额、冻结金额、可用余额
- **积分信息**：当前积分、积分等级、积分变化趋势
- **统计数据**：发布数量、交易次数、信用评分
- **快捷操作**：充值、提现、实名认证等

##### 3.2.2 积分管理 (Integral Management)
- **积分余额**：当前可用积分和总积分
- **积分明细**：积分获取和消费记录
- **积分规则**：积分获取和使用规则说明
- **积分商城**：积分兑换商品或服务
- **等级体系**：积分等级和对应权益

##### 3.2.3 库存管理 (Inventory Management)
- **库存列表**：用户拥有的物资库存
- **分类管理**：按物资类型分类显示
- **库存操作**：入库、出库、调整数量
- **库存预警**：低库存提醒功能
- **库存报表**：库存变化统计和分析

#### 3.3 数据结构
```typescript
interface AccountInfo {
  userId: string;
  username: string;
  avatar?: string;
  level: number;
  balance: number;
  frozenAmount: number;
  integral: number;
  integralLevel: number;
  publishCount: number;
  tradeCount: number;
  creditScore: number;
}

interface IntegralRecord {
  id: string;
  type: 'EARN' | 'SPEND';
  amount: number;
  description: string;
  createTime: string;
  balance: number;
}

interface InventoryItem {
  id: string;
  name: string;
  category: string;
  quantity: number;
  unit: string;
  price: number;
  location: string;
  status: 'NORMAL' | 'LOW_STOCK' | 'OUT_OF_STOCK';
  lastUpdateTime: string;
}
```

### 4. 消息中心板块 (Message Center)

#### 4.1 功能概述
用户消息管理中心，支持私信、系统通知、交易消息等多种消息类型的管理。

#### 4.2 子模块详细需求

##### 4.2.1 收件箱 (Inbox)
- **消息列表**：接收到的所有消息
- **消息分类**：系统消息、私信、交易消息等
- **未读标识**：未读消息数量和标识
- **消息搜索**：按发送人、标题、内容搜索
- **批量操作**：批量标记已读、批量删除
- **消息详情**：查看消息完整内容和附件

##### 4.2.2 发件箱 (Outbox)
- **已发消息**：用户发送的所有消息
- **发送状态**：已发送、发送失败等状态
- **消息撤回**：支持撤回未读消息
- **发送记录**：消息发送时间和状态记录

##### 4.2.3 系统消息 (System Message)
- **系统通知**：平台公告、政策更新等
- **业务通知**：交易提醒、审核结果等
- **安全提醒**：登录异常、密码修改等
- **营销消息**：活动推广、优惠信息等

#### 4.3 消息类型定义
```typescript
enum MessageType {
  SYSTEM = 'SYSTEM',      // 系统消息
  PRIVATE = 'PRIVATE',    // 私信
  TRADE = 'TRADE',        // 交易消息
  NOTICE = 'NOTICE',      // 通知消息
  MARKETING = 'MARKETING' // 营销消息
}

interface MessageItem {
  id: string;
  type: MessageType;
  title: string;
  content: string;
  sender: string;
  senderName: string;
  receiver: string;
  isRead: boolean;
  createTime: string;
  attachments?: string[];
}
```

## 🎨 UI/UX 设计要求

### 设计原则
1. **一致性**：保持与灰谷网项目整体风格一致
2. **易用性**：界面简洁，操作流程清晰
3. **响应式**：支持PC端和移动端适配
4. **可访问性**：支持键盘导航和屏幕阅读器

### 色彩规范
- **主色调**：沿用灰谷网项目的主题色
- **辅助色**：成功绿色、警告橙色、错误红色
- **中性色**：灰色系用于文本和边框

### 组件规范
- **按钮**：统一的按钮样式和交互效果
- **表单**：统一的表单控件和验证样式
- **表格**：统一的表格样式和分页组件
- **卡片**：统一的卡片布局和阴影效果

## 🔧 技术要求

### 前端技术栈
- **框架**：Vue 3 + TypeScript
- **UI库**：Ant Design Vue
- **状态管理**：Pinia
- **路由**：Vue Router 4
- **HTTP客户端**：Axios
- **构建工具**：Vite

### 代码规范
- **命名规范**：使用驼峰命名法
- **文件结构**：按功能模块组织文件
- **组件设计**：单一职责，高内聚低耦合
- **类型定义**：完整的TypeScript类型定义

### 性能要求
- **首屏加载**：< 3秒
- **页面切换**：< 1秒
- **接口响应**：< 2秒
- **内存占用**：合理的内存使用

## 📱 兼容性要求

### 浏览器支持
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### 设备支持
- **PC端**：1920x1080及以上分辨率
- **平板**：768px-1024px宽度
- **手机**：375px-768px宽度

## 🔒 安全要求

### 数据安全
- **输入验证**：所有用户输入进行验证和过滤
- **XSS防护**：防止跨站脚本攻击
- **CSRF防护**：防止跨站请求伪造
- **数据加密**：敏感数据传输加密

### 权限控制
- **身份验证**：用户登录状态验证
- **权限验证**：操作权限检查
- **数据隔离**：用户数据访问隔离

## 📊 监控和分析

### 用户行为分析
- **页面访问**：统计各页面访问量
- **功能使用**：统计功能使用频率
- **用户路径**：分析用户操作路径
- **转化率**：统计关键操作转化率

### 性能监控
- **页面性能**：监控页面加载时间
- **接口性能**：监控API响应时间
- **错误监控**：收集和分析错误信息
- **资源监控**：监控静态资源加载
