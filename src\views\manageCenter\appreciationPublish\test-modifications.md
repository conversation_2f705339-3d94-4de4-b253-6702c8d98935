# 增值发布页面修改说明

## 修改内容

### 1. 根据委托单号前四位判断接口调用

- **WTCZ前缀**: 调用原有的 `queryEntrustById` 接口获取资产处置数据
- **WTCG前缀**: 调用新的 `/hgy/entrustService/hgyProcurement/queryProcurementById` 接口获取采购信息数据

### 2. 服务类型自动识别

- 服务类型选择框已禁用，不允许用户手动选择
- 根据委托单号前四位自动设置服务类型：
  - WTCZ → 发布资产处置 (serviceType = 2)
  - WTCG → 发布采购信息 (serviceType = 3)

### 3. 修改的文件

#### appreciationPublish.ts API文件修改：

1. 添加新的采购发布接口函数 `platformUpdateProcurement`
2. 在 `PlatformUpdateParams` 接口中添加 `onEntrustCompanyId` 字段

#### index.vue 主要修改：

1. 从API文件导入新的采购发布接口 `platformUpdateProcurement`
2. 修改 `fetchEntrustDetail` 函数，根据委托单号前缀判断调用不同接口
3. 修改 `submitForm` 函数，根据服务类型调用不同的发布接口
4. 简化 `handleServiceTypeChange` 函数，因为不再支持手动切换
5. 优化委托单号变化监听逻辑
6. 添加附件回显逻辑，处理 `hgyAttachmentList` 字段数据
7. 修正委托相关ID字段的获取逻辑：
   - `entrustCompanyId` 从 `hgyEntrustOrder.entrustCompanyId` 获取（委托单位ID）
   - `onEntrustCompanyId` 从 `hgyEntrustOrder.onEntrustCompanyId` 获取（委托企业ID）
8. 添加发布成功后的处理逻辑：
   - 重置所有表单数据到初始状态
   - 回到第一步页面
   - 重新获取委托单列表
9. 修正企业名称显示逻辑：
   - `hgyEntrustOrder.entrustCompanyName` → 委托单位名称
   - `hgyEntrustOrder.onEntrustCompanyName` → 处置单位名称（受委托单位）
   - 表单字段映射：title显示处置单位，type显示委托单位
10. 添加联系人信息回显逻辑，从 `hgyEntrustOrder.relationUser` 和 `hgyEntrustOrder.relationPhone` 获取数据

#### Step1.vue 主要修改：

1. 禁用服务类型选择框
2. 修改提示文字为"服务类型根据委托单号自动识别，无需手动选择"
3. 添加对应的样式支持
4. 优化 `processAttachmentList` 函数，根据服务类型处理不同的附件回显逻辑：
   - 资产处置：根据 `fileType` 分离图片和附件
   - 采购信息：所有附件都作为采购附件处理

### 4. 功能流程

#### 数据获取流程：

1. 用户选择委托单号
2. 系统根据委托单号前四位自动判断服务类型
3. 根据服务类型调用对应的接口获取详情数据：
   - WTCZ → 调用 `/hgy/entrustService/hgyAssetEntrust/queryEntrustById`
   - WTCG → 调用 `/hgy/entrustService/hgyProcurement/queryProcurementById`
4. 自动回显相关信息到表单中

#### 数据提交流程：

1. 用户填写完表单信息点击保存/发布
2. 系统根据当前服务类型调用对应的发布接口：
   - 资产处置 → 调用 `/hgy/entrustService/hgyAssetEntrust/platformUpdate`
   - 采购信息 → 调用 `/hgy/entrustService/hgyProcurement/platformUpdate`
3. 提交成功后跳转到列表页面

### 5. 测试要点

#### 数据获取测试：

- 选择WTCZ开头的委托单号，应该显示"发布资产处置"并调用资产处置查询接口
- 选择WTCG开头的委托单号，应该显示"发布采购信息"并调用采购信息查询接口
- 服务类型选择框应该是禁用状态
- 委托单号变化时应该正确清空之前的数据并重新获取

#### 数据提交测试：

- 资产处置类型的数据应该调用资产处置发布接口进行保存/发布
- 采购信息类型的数据应该调用采购信息发布接口进行保存/发布
- 检查控制台日志确认调用了正确的接口
- 验证提交成功后能正确重置表单数据并回到首页面
- 验证提交数据中包含正确的委托相关ID字段：
  - `entrustCompanyId`（委托单位ID，从 `hgyEntrustOrder.entrustCompanyId` 获取）
  - `onEntrustCompanyId`（委托企业ID，从 `hgyEntrustOrder.onEntrustCompanyId` 获取）

#### 附件回显测试：

- 资产处置情况下：验证标的图片和附件能正确分离和回显
  - `fileType: "image"` 的文件应该显示在标的图片区域
  - 其他类型的文件应该显示在附件上传区域
- 采购信息情况下：验证所有附件都能正确回显到采购附件区域
- 检查控制台日志确认附件处理逻辑正确执行

#### 联系人信息回显测试：

- 验证WTCZ委托单的联系人姓名和电话能正确回显到第二步表单
- 验证WTCG委托单的联系人姓名和电话能正确回显到第二步表单
- 验证委托单号切换时联系人信息能正确清空和重新获取
- 检查控制台日志确认联系人信息回显逻辑正确执行

#### 企业信息回显测试：

- 验证处置单位名称正确显示在title字段（来自 `hgyEntrustOrder.onEntrustCompanyName`）
- 验证委托单位名称正确显示在type字段（来自 `hgyEntrustOrder.entrustCompanyName`）
- 验证委托相关ID正确获取和提交：
  - 委托单位ID（来自 `hgyEntrustOrder.entrustCompanyId`）
  - 委托企业ID（来自 `hgyEntrustOrder.onEntrustCompanyId`）
- 检查控制台日志确认企业信息字段映射正确

#### 发布成功后处理测试：

- 验证发布成功后表单数据能正确重置到初始状态
- 验证发布成功后能回到第一步页面
- 验证发布成功后委托单列表能重新加载
- 验证发布成功后用户可以重新选择委托单号进行新的发布
- 检查控制台日志确认重置逻辑正确执行
