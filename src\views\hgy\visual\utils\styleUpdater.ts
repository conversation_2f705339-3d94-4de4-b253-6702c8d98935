/**
 * 样式更新工具
 * 用于批量更新样式文件，添加全屏/非全屏状态的CSS类名前缀
 */

// 需要更新的组件样式文件列表
export const COMPONENT_STYLE_FILES = [
  'AssetRanking.less',
  'BottomCharts.less', 
  'ChartSection.less',
  'CountTo.less',
  'DataCards.less',
  'MainKPICards.less',
  'RankingSection.less',
  'SmallCards.less',
  'TargetRanking.less',
  'VisualHeader.less'
];

// 样式更新规则
export const STYLE_UPDATE_RULES = {
  fullScreen: {
    // 全屏状态下的CSS类名前缀
    containerPrefix: '.visual-container.fullscreen',
    comment: '// 全屏状态下的样式 - 1658px × 994px'
  },
  notFullScreen: {
    // 非全屏状态下的CSS类名前缀  
    containerPrefix: '.visual-container:not(.fullscreen)',
    comment: '// 非全屏状态下的样式 - 1658px × 821px'
  }
};

/**
 * 更新样式文件的CSS选择器
 * @param content 原始文件内容
 * @param mode 模式：'fullScreen' 或 'notFullScreen'
 * @returns 更新后的文件内容
 */
export function updateStyleSelectors(content: string, mode: 'fullScreen' | 'notFullScreen'): string {
  const rule = STYLE_UPDATE_RULES[mode];
  
  // 在文件开头添加注释
  let updatedContent = `${rule.comment}\n${content}`;
  
  // 更新顶级选择器，添加容器前缀
  // 匹配以 . 开头的CSS类选择器（顶级）
  updatedContent = updatedContent.replace(
    /^(\s*)(\.[a-zA-Z][a-zA-Z0-9_-]*)\s*\{/gm,
    (match, indent, selector) => {
      // 如果已经包含 .visual-container，则不重复添加
      if (selector.includes('.visual-container')) {
        return match;
      }
      // 添加容器前缀
      return `${indent}${rule.containerPrefix} ${selector} {`;
    }
  );
  
  return updatedContent;
}

/**
 * 生成样式更新说明
 */
export function generateUpdateInstructions(): string {
  return `
# 样式文件更新说明

## 更新目标
将现有的样式文件修改为支持全屏/非全屏状态切换的格式。

## 更新规则

### 全屏状态样式 (fullScreen/)
- 容器选择器前缀: \`.visual-container.fullscreen\`
- 设计尺寸: 1658px × 994px
- 适用场景: 浏览器全屏模式

### 非全屏状态样式 (notFullScreen/)  
- 容器选择器前缀: \`.visual-container:not(.fullscreen)\`
- 设计尺寸: 1658px × 821px
- 适用场景: 有浏览器状态栏的窗口模式

## 需要更新的文件
${COMPONENT_STYLE_FILES.map(file => `- ${file}`).join('\n')}

## 更新示例

### 更新前:
\`\`\`less
.header-content {
  padding: 20px;
  height: 88px;
}
\`\`\`

### 更新后 (全屏):
\`\`\`less
// 全屏状态下的样式 - 1658px × 994px
.visual-container.fullscreen .header-content {
  padding: 20px;
  height: 88px;
}
\`\`\`

### 更新后 (非全屏):
\`\`\`less
// 非全屏状态下的样式 - 1658px × 821px
.visual-container:not(.fullscreen) .header-content {
  padding: 20px;
  height: 88px;
}
\`\`\`

## 注意事项
1. 只更新顶级CSS类选择器
2. 已包含 .visual-container 的选择器不重复添加前缀
3. 保持原有的嵌套结构和样式属性不变
4. 在文件开头添加对应的注释说明
`;
}

/**
 * 检查文件是否需要更新
 * @param content 文件内容
 * @param mode 模式
 * @returns 是否需要更新
 */
export function needsUpdate(content: string, mode: 'fullScreen' | 'notFullScreen'): boolean {
  const rule = STYLE_UPDATE_RULES[mode];
  
  // 检查是否已经包含对应的容器前缀
  return !content.includes(rule.containerPrefix);
}

/**
 * 验证更新后的内容
 * @param content 更新后的内容
 * @param mode 模式
 * @returns 验证结果
 */
export function validateUpdatedContent(content: string, mode: 'fullScreen' | 'notFullScreen'): {
  isValid: boolean;
  issues: string[];
} {
  const issues: string[] = [];
  const rule = STYLE_UPDATE_RULES[mode];
  
  // 检查是否包含注释
  if (!content.includes(rule.comment)) {
    issues.push('缺少文件头部注释');
  }
  
  // 检查是否包含容器前缀
  if (!content.includes(rule.containerPrefix)) {
    issues.push('缺少容器选择器前缀');
  }
  
  // 检查是否有未更新的顶级选择器
  const topLevelSelectors = content.match(/^\s*\.[a-zA-Z][a-zA-Z0-9_-]*\s*\{/gm);
  if (topLevelSelectors) {
    const unupdatedSelectors = topLevelSelectors.filter(selector => 
      !selector.includes('.visual-container')
    );
    if (unupdatedSelectors.length > 0) {
      issues.push(`发现未更新的顶级选择器: ${unupdatedSelectors.join(', ')}`);
    }
  }
  
  return {
    isValid: issues.length === 0,
    issues
  };
}

export default {
  COMPONENT_STYLE_FILES,
  STYLE_UPDATE_RULES,
  updateStyleSelectors,
  generateUpdateInstructions,
  needsUpdate,
  validateUpdatedContent
};
