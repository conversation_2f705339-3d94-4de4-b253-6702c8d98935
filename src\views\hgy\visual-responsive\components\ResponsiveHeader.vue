<template>
  <div class="responsive-header">
    <!-- 头部背景 -->
    <div class="header-bg"></div>
    
    <!-- 头部内容 -->
    <div class="header-content">
      <!-- 左侧导航按钮 -->
      <div class="header-left">
        <!-- 物资种类选择 -->
        <div class="nav-button material-select" v-if="!isMobile">
          <a-cascader
            v-model:value="selectedMaterial"
            :options="materialTypeOptions"
            placeholder="请选择物资种类"
            show-search
            :field-names="{ label: 'name', value: 'id', children: 'children' }"
            change-on-select
            :filter-option="filterMaterialType"
            @change="handleMaterialChange"
            :dropdown-style="dropdownStyle"
            :dropdown-class-name="'responsive-cascader-dropdown'"
          >
            <div class="button-content">
              <span class="button-text">{{ selectedMaterialText || '物资种类' }}</span>
              <DownOutlined class="button-icon" />
            </div>
          </a-cascader>
        </div>
        
        <!-- 省市区选择 -->
        <div class="nav-button address-select" v-if="!isMobile">
          <a-cascader
            v-model:value="selectedArea"
            :options="areaOptions"
            placeholder="选择省市区"
            @change="handleAreaChange"
            :dropdown-style="dropdownStyle"
            :dropdown-class-name="'responsive-cascader-dropdown'"
          >
            <div class="button-content">
              <span class="button-text">{{ selectedAreaText || '省市区' }}</span>
              <DownOutlined class="button-icon" />
            </div>
          </a-cascader>
        </div>
        
        <!-- 移动端菜单按钮 -->
        <div class="mobile-menu-button" v-if="isMobile" @click="toggleMobileMenu">
          <MenuOutlined />
        </div>
      </div>

      <!-- 中间标题 -->
      <div class="header-center">
        <h1 class="main-title">
          <span class="title-text">灰谷网经营数据可视化大屏</span>
          <span class="title-subtitle" v-if="!isMobile">Data Visualization Dashboard</span>
        </h1>
      </div>

      <!-- 右侧信息 -->
      <div class="header-right">
        <!-- 时间显示 -->
        <div class="time-info">
          <div class="current-time">{{ currentTime }}</div>
          <div class="current-date" v-if="!isMobile">{{ currentDate }}</div>
        </div>
        
        <!-- 全屏按钮 -->
        <div class="fullscreen-button" @click="toggleFullscreen" v-if="!isMobile">
          <FullscreenOutlined v-if="!isFullscreen" />
          <FullscreenExitOutlined v-else />
        </div>
      </div>
    </div>
    
    <!-- 移动端下拉菜单 -->
    <div class="mobile-menu" v-if="isMobile && showMobileMenu">
      <div class="mobile-menu-item">
        <a-cascader
          v-model:value="selectedMaterial"
          :options="materialTypeOptions"
          placeholder="请选择物资种类"
          show-search
          :field-names="{ label: 'name', value: 'id', children: 'children' }"
          change-on-select
          :filter-option="filterMaterialType"
          @change="handleMaterialChange"
          :dropdown-style="dropdownStyle"
        >
          <div class="mobile-select-button">
            <span>{{ selectedMaterialText || '请选择物资种类' }}</span>
            <DownOutlined />
          </div>
        </a-cascader>
      </div>
      
      <div class="mobile-menu-item">
        <a-cascader
          v-model:value="selectedArea"
          :options="areaOptions"
          placeholder="选择省市区"
          @change="handleAreaChange"
          :dropdown-style="dropdownStyle"
        >
          <div class="mobile-select-button">
            <span>{{ selectedAreaText || '选择省市区' }}</span>
            <DownOutlined />
          </div>
        </a-cascader>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, inject } from 'vue';
import { DownOutlined, MenuOutlined, FullscreenOutlined, FullscreenExitOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import { useFullscreen } from '../composables/useFullscreen';

// 注入响应式数据
const screenSize = inject('screenSize') as any;
const breakpoint = inject('breakpoint') as any;

// 全屏功能
const { isFullscreen, toggleFullscreen } = useFullscreen();

// 响应式状态
const isMobile = computed(() => screenSize?.width < 768);
const showMobileMenu = ref(false);

// 时间相关
const currentTime = ref('');
const currentDate = ref('');
let timeInterval: NodeJS.Timeout;

// 选择器数据
const selectedMaterial = ref<string[]>([]);
const selectedArea = ref<string[]>([]);
const selectedMaterialText = ref('');
const selectedAreaText = ref('');

// 物资类型选项（示例数据）
const materialTypeOptions = ref([
  {
    id: '1',
    name: '建筑材料',
    children: [
      { id: '1-1', name: '钢材' },
      { id: '1-2', name: '水泥' },
      { id: '1-3', name: '砂石' },
    ],
  },
  {
    id: '2',
    name: '机械设备',
    children: [
      { id: '2-1', name: '挖掘机' },
      { id: '2-2', name: '装载机' },
      { id: '2-3', name: '起重机' },
    ],
  },
]);

// 地区选项（示例数据）
const areaOptions = ref([
  {
    value: '41',
    label: '河南省',
    children: [
      {
        value: '4101',
        label: '郑州市',
        children: [
          { value: '410102', label: '中原区' },
          { value: '410103', label: '二七区' },
          { value: '410104', label: '管城区' },
        ],
      },
    ],
  },
]);

// 下拉框样式
const dropdownStyle = computed(() => ({
  background: 'rgba(10, 14, 39, 0.95)',
  border: '1px solid var(--primary-color)',
  backdropFilter: 'blur(10px)',
  borderRadius: '8px',
}));

// 事件定义
const emit = defineEmits<{
  materialChange: [value: string[], text: string];
  areaChange: [value: string[], text: string];
}>();

// 更新时间
const updateTime = () => {
  const now = dayjs();
  currentTime.value = now.format('HH:mm:ss');
  currentDate.value = now.format('YYYY年MM月DD日');
};

// 物资类型筛选
const filterMaterialType = (inputValue: string, path: any[]) => {
  return path.some(option => option.name.toLowerCase().includes(inputValue.toLowerCase()));
};

// 处理物资类型变化
const handleMaterialChange = (value: string[], selectedOptions: any[]) => {
  selectedMaterialText.value = selectedOptions.map(option => option.name).join(' > ');
  emit('materialChange', value, selectedMaterialText.value);
  if (isMobile.value) {
    showMobileMenu.value = false;
  }
};

// 处理地区变化
const handleAreaChange = (value: string[], selectedOptions: any[]) => {
  selectedAreaText.value = selectedOptions.map(option => option.label).join(' > ');
  emit('areaChange', value, selectedAreaText.value);
  if (isMobile.value) {
    showMobileMenu.value = false;
  }
};

// 切换移动端菜单
const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value;
};

// 点击外部关闭移动端菜单
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  if (!target.closest('.responsive-header')) {
    showMobileMenu.value = false;
  }
};

onMounted(() => {
  updateTime();
  timeInterval = setInterval(updateTime, 1000);
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval);
  }
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style lang="less" scoped>
@import '../styles/responsive-variables.less';
@import '../styles/responsive-mixins.less';

.responsive-header {
  position: relative;
  width: 100%;
  height: var(--header-height);
  overflow: visible;
  z-index: 10;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/header_1754987780270.png') no-repeat center center;
  background-size: cover;
  opacity: 0.8;
  z-index: 1;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-bg);
    border-bottom: 1px solid var(--border-glow);
  }
}

.header-content {
  position: relative;
  z-index: 2;
  height: 100%;
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  align-items: center;
  padding: 0 var(--spacing-lg);
  
  .mobile-first(md) {
    .content() {
      padding: 0 var(--spacing-xl);
    }
  }
  
  .respond-to(xs) {
    .content() {
      grid-template-columns: auto 1fr auto;
      padding: 0 var(--spacing-sm);
    }
  }
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  justify-self: start;
  
  .respond-to(xs) {
    .content() {
      gap: var(--spacing-sm);
    }
  }
}

.nav-button {
  .responsive-card-bg('');
  height: 40px;
  min-width: 120px;
  cursor: pointer;
  transition: all var(--duration-normal) ease;
  
  &:hover {
    .glow-effect(var(--primary-color), 12px, 0.4);
    transform: translateY(-1px);
  }
  
  .button-content {
    position: relative;
    z-index: 3;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--spacing-md);
    color: var(--text-light);
    .responsive-font-size(var(--font-sm));
  }
  
  .button-text {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .button-icon {
    margin-left: var(--spacing-xs);
    opacity: 0.7;
  }
  
  .mobile-first(md) {
    .content() {
      min-width: 140px;
    }
  }
}

.mobile-menu-button {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--card-bg);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  color: var(--text-light);
  cursor: pointer;
  transition: all var(--duration-normal) ease;
  
  &:hover {
    background: var(--primary-color-10);
    border-color: var(--primary-color);
  }
}

.header-center {
  justify-self: center;
  text-align: center;
}

.main-title {
  margin: 0;
  color: var(--text-light);
  .text-glow(var(--primary-color), 12px, 0.6);
  
  .title-text {
    display: block;
    .responsive-font-size(var(--font-2xl));
    font-weight: bold;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .title-subtitle {
    display: block;
    .responsive-font-size(var(--font-xs));
    color: var(--text-secondary);
    margin-top: 2px;
    font-weight: normal;
  }
  
  .respond-to(xs) {
    .content() {
      .title-text {
        font-size: var(--font-lg);
      }
    }
  }
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  justify-self: end;
}

.time-info {
  text-align: right;
  color: var(--text-light);
  
  .current-time {
    .responsive-font-size(var(--font-lg));
    font-weight: bold;
    .text-glow(var(--primary-color), 8px, 0.5);
  }
  
  .current-date {
    .responsive-font-size(var(--font-xs));
    color: var(--text-secondary);
    margin-top: 2px;
  }
}

.fullscreen-button {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--card-bg);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  color: var(--text-light);
  cursor: pointer;
  transition: all var(--duration-normal) ease;
  
  &:hover {
    background: var(--primary-color-10);
    border-color: var(--primary-color);
    .glow-effect(var(--primary-color), 8px, 0.3);
  }
}

.mobile-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--bg-dark-80);
  border: 1px solid var(--border-light);
  border-top: none;
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
  backdrop-filter: blur(10px);
  z-index: 100;
  padding: var(--spacing-md);
  
  .mobile-menu-item {
    margin-bottom: var(--spacing-sm);
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .mobile-select-button {
    width: 100%;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--spacing-md);
    background: var(--card-bg);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    color: var(--text-light);
    cursor: pointer;
    transition: all var(--duration-normal) ease;
    
    &:hover {
      background: var(--primary-color-10);
      border-color: var(--primary-color);
    }
  }
}

// 全局下拉框样式
:global(.responsive-cascader-dropdown) {
  .ant-cascader-menu {
    background: rgba(10, 14, 39, 0.95) !important;
    border-right: 1px solid var(--primary-color-20) !important;
  }
  
  .ant-cascader-menu-item {
    color: var(--text-light) !important;
    
    &:hover {
      background: var(--primary-color-10) !important;
    }
    
    &.ant-cascader-menu-item-active {
      background: var(--primary-color-20) !important;
    }
  }
}</style>
