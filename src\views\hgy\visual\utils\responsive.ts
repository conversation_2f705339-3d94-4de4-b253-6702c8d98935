/**
 * 数据可视化大屏响应式适配工具
 */

// 设计稿尺寸
export const DESIGN_WIDTH = 1658;
export const DESIGN_HEIGHT = 994;

// 最小缩放比例
export const MIN_SCALE = 0.5;
export const MAX_SCALE = 2;

/**
 * 计算缩放比例
 */
export function calculateScale(): { scale: number; width: number; height: number } {
  // 获取容器的实际可用空间
  const container = document.querySelector('.visual-container') as HTMLElement;
  let currentWidth = window.innerWidth;
  let currentHeight = window.innerHeight;

  // 无论是否全屏，都基于容器的父元素（main内容区域）来计算尺寸
  if (container) {
    const containerRect = container.getBoundingClientRect();
    const containerParent = container.parentElement;

    if (containerParent) {
      const parentRect = containerParent.getBoundingClientRect();
      currentWidth = parentRect.width;
      currentHeight = parentRect.height;
    }

    // 如果容器有明确的尺寸，使用容器尺寸
    if (containerRect.width > 0 && containerRect.height > 0) {
      currentWidth = containerRect.width;
      currentHeight = containerRect.height;
    }

    // 全屏模式下同样根据容器尺寸计算缩放比例（不强制1倍）
    // 保留容器尺寸计算逻辑，避免提前返回
  }

  // 计算宽高比例
  const scaleX = currentWidth / DESIGN_WIDTH;
  const scaleY = currentHeight / DESIGN_HEIGHT;

  // 使用 cover 策略：取较大的比例，铺满容器，可能产生裁切
  let scale = Math.max(scaleX, scaleY);

  // 限制缩放范围
  scale = Math.max(MIN_SCALE, Math.min(MAX_SCALE, scale));

  return {
    scale,
    width: DESIGN_WIDTH * scale,
    height: DESIGN_HEIGHT * scale,
  };
}

/**
 * 应用响应式缩放
 */
export function applyResponsiveScale(element: HTMLElement | null): void {
  if (!element) return;

  const { scale } = calculateScale();
  const container = element.closest('.visual-container') as HTMLElement;
  const containerRect = container?.getBoundingClientRect();
  const containerHeight = containerRect?.height || window.innerHeight;
  const scaledHeight = DESIGN_HEIGHT * scale;

  // cover + 高度溢出纵向滚动：
  // - 若高度溢出：顶部对齐，允许纵向滚动；
  // - 若未溢出：垂直居中，隐藏滚动。
  if (scaledHeight > containerHeight) {
    element.style.transform = `translate(-50%, 0) scale(${scale})`;
    element.style.transformOrigin = 'top center';
    element.style.top = '0';
    if (container) {
      container.style.position = 'relative';
      container.style.overflowX = 'hidden';
      container.style.overflowY = 'auto';
    }
  } else {
    element.style.transform = `translate(-50%, -50%) scale(${scale})`;
    element.style.transformOrigin = 'center center';
    element.style.top = '50%';
    if (container) {
      container.style.position = 'relative';
      container.style.overflow = 'hidden';
    }
  }
}

/**
 * 获取当前设备类型
 */
export function getDeviceType(): 'desktop' | 'tablet' | 'mobile' {
  const width = window.innerWidth;

  if (width >= 1024) {
    return 'desktop';
  } else if (width >= 768) {
    return 'tablet';
  } else {
    return 'mobile';
  }
}

/**
 * 根据设备类型调整布局
 */
export function adjustLayoutForDevice(): {
  containerClass: string;
  showMobileLayout: boolean;
  chartHeight: number;
} {
  const deviceType = getDeviceType();
  const { scale } = calculateScale();

  switch (deviceType) {
    case 'mobile':
      return {
        containerClass: 'mobile-layout',
        showMobileLayout: true,
        chartHeight: 200,
      };
    case 'tablet':
      return {
        containerClass: 'tablet-layout',
        showMobileLayout: false,
        chartHeight: 250,
      };
    default:
      return {
        containerClass: scale < 0.8 ? 'compact-layout' : 'desktop-layout',
        showMobileLayout: false,
        chartHeight: 300,
      };
  }
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;

  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };

    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(func: T, limit: number): (...args: Parameters<T>) => void {
  let inThrottle: boolean;

  return function executedFunction(this: ThisParameterType<T>, ...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

/**
 * 响应式适配 Hook
 */
export function useResponsive() {
  const updateScale = () => {
    const container = document.querySelector('.visual-content') as HTMLElement;
    applyResponsiveScale(container);
  };

  const debouncedUpdateScale = debounce(updateScale, 100);
  const throttledUpdateScale = throttle(updateScale, 16); // 60fps

  return {
    updateScale,
    debouncedUpdateScale,
    throttledUpdateScale,
    calculateScale,
    getDeviceType,
    adjustLayoutForDevice,
  };
}

/**
 * 字体大小适配
 */
export function getResponsiveFontSize(baseFontSize: number): number {
  const { scale } = calculateScale();
  return Math.max(12, baseFontSize * scale);
}

/**
 * 间距适配
 */
export function getResponsiveSpacing(baseSpacing: number): number {
  const { scale } = calculateScale();
  return Math.max(4, baseSpacing * scale);
}

/**
 * 检查是否为高分辨率屏幕
 */
export function isHighDPI(): boolean {
  return window.devicePixelRatio > 1;
}

/**
 * 获取最佳图表配置
 */
export function getOptimalChartConfig() {
  const { scale } = calculateScale();
  const deviceType = getDeviceType();
  const isHighRes = isHighDPI();

  return {
    // 图表尺寸
    chartSize: {
      width: deviceType === 'mobile' ? '100%' : 'auto',
      height: deviceType === 'mobile' ? 200 : 300,
    },

    // 字体配置
    fontSize: {
      title: getResponsiveFontSize(deviceType === 'mobile' ? 14 : 18),
      axis: getResponsiveFontSize(deviceType === 'mobile' ? 10 : 12),
      legend: getResponsiveFontSize(deviceType === 'mobile' ? 10 : 12),
      tooltip: getResponsiveFontSize(deviceType === 'mobile' ? 12 : 14),
    },

    // 间距配置
    spacing: {
      padding: getResponsiveSpacing(deviceType === 'mobile' ? 10 : 20),
      margin: getResponsiveSpacing(deviceType === 'mobile' ? 5 : 10),
    },

    // 动画配置
    animation: {
      duration: scale < 0.7 ? 1000 : 2000, // 小屏幕减少动画时间
      enabled: deviceType !== 'mobile', // 移动端可能禁用动画以提升性能
    },

    // 渲染配置
    renderer: isHighRes ? 'canvas' : 'svg',
    devicePixelRatio: isHighRes ? 2 : 1,
  };
}
