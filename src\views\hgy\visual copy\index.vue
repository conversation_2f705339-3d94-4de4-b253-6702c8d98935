<!--
  数据可视化大屏主页面

  功能特性：
  1. 全屏显示模式，自动进入全屏，ESC退出返回首页
  2. 响应式适配，基于1658x994设计稿进行缩放适配
  3. 异形卡片背景，使用设计图提供的背景图片
  4. 数字滚动动画效果
  5. ECharts图表展示，包含渐变色彩和动画
  6. 实时数据更新和性能优化

  布局结构：
  - 头部：标题、时间、导航信息
  - 左侧：数据指标卡片（成交总额、溢价总额等）
  - 中间：图表区域（趋势图、柱状图等）
  - 右侧：排名列表（溢价额排名、溢价率排名等）
-->
<template>
  <div class="visual-container" :class="{ fullscreen: isFullscreen }">
    <!-- 星空背景效果 -->
    <!-- 主背景图片 -->
    <div class="visual-bg"></div>

    <!-- 主要内容区域 - 固定1658x994尺寸，通过transform缩放适配 -->
    <div class="visual-content">
      <!-- 头部区域 -->
      <div class="visual-header">
        <VisualHeader @material-change="handleMaterialChange" @area-change="handleAreaChange" />
      </div>

      <!-- 中间内容区域 -->
      <div class="visual-middle">
        <!-- 左侧：标的数据排名 -->
        <div class="visual-left">
          <TargetRanking />
        </div>

        <!-- 中间：数据指标和小卡片 -->
        <div class="visual-center">
          <div>
            <!-- 上部：成交总额、溢价总额、总溢价率 -->
            <div class="center-top">
              <MainKPICards />
            </div>
            <!-- 下部：10个小卡片 -->
            <div class="center-bottom">
              <SmallCards />
            </div>
            <!-- 背景图 -->
            <div class="center-bg"></div>
          </div>
        </div>

        <!-- 右侧：资产处置数据排名 -->
        <div class="visual-right">
          <AssetRanking />
        </div>
      </div>

      <!-- 底部区域：三个图表卡片 -->
      <div class="visual-footer">
        <BottomCharts />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted, provide } from 'vue';
  import { useRouter } from 'vue-router';
  import VisualHeader from './components/VisualHeader.vue';
  import TargetRanking from './components/TargetRanking.vue';
  import MainKPICards from './components/MainKPICards.vue';
  import SmallCards from './components/SmallCards.vue';
  import AssetRanking from './components/AssetRanking.vue';
  import BottomCharts from './components/BottomCharts.vue';
  import { useResponsive } from './utils/responsive';
  import { dataService } from './services/dataService';
  import { performanceMonitor, imagePreloader, getOptimizationConfig } from './utils/performance';

  const router = useRouter();
  const isFullscreen = ref(false);
  const resizeObserver = ref<ResizeObserver | null>(null);

  // 性能优化配置
  const optimizationConfig = getOptimizationConfig();

  // 提供数据服务给子组件
  provide('dataService', dataService);
  provide('optimizationConfig', optimizationConfig);

  // 进入全屏
  const enterFullscreen = () => {
    const element = document.documentElement;
    if (element.requestFullscreen) {
      element.requestFullscreen();
    } else if ((element as any).webkitRequestFullscreen) {
      (element as any).webkitRequestFullscreen();
    } else if ((element as any).msRequestFullscreen) {
      (element as any).msRequestFullscreen();
    }
    isFullscreen.value = true;
  };

  // 退出全屏
  const exitFullscreen = () => {
    if (document.exitFullscreen) {
      document.exitFullscreen();
    } else if ((document as any).webkitExitFullscreen) {
      (document as any).webkitExitFullscreen();
    } else if ((document as any).msExitFullscreen) {
      (document as any).msExitFullscreen();
    }
    isFullscreen.value = false;
  };

  // 控制数据大屏全屏显示
  const toggleVisualFullscreen = (isFullscreen: boolean) => {
    const visualContainer = document.querySelector('.visual-container') as HTMLElement;

    if (isFullscreen) {
      // 进入全屏：只是标记全屏状态，不改变布局
      if (visualContainer) {
        visualContainer.classList.add('fullscreen');
      }
    } else {
      // 退出全屏：移除全屏状态标记
      if (visualContainer) {
        visualContainer.classList.remove('fullscreen');
      }
    }
  };

  // 监听全屏状态变化
  const handleFullscreenChange = () => {
    const isCurrentlyFullscreen = !!(
      document.fullscreenElement ||
      (document as any).webkitFullscreenElement ||
      (document as any).msFullscreenElement
    );
    isFullscreen.value = isCurrentlyFullscreen;

    // 控制数据大屏的全屏显示
    toggleVisualFullscreen(isCurrentlyFullscreen);

    // 只有在主动进入全屏后退出时才返回首页
    // 如果是通过URL参数进入的全屏模式，退出时返回首页
    const urlParams = new URLSearchParams(window.location.search);
    if (!isCurrentlyFullscreen && urlParams.get('fullscreen') === 'true') {
      router.push('/dashboard/analysis');
    }
  };

  // 键盘事件监听
  const handleKeydown = (event: KeyboardEvent) => {
    if (event.key === 'Escape' && isFullscreen.value) {
      exitFullscreen();
    } else if (event.key === 'F11') {
      event.preventDefault(); // 阻止浏览器默认的F11行为
      if (isFullscreen.value) {
        exitFullscreen();
      } else {
        enterFullscreen();
      }
    }
  };

  // 响应式适配
  const { debouncedUpdateScale } = useResponsive();

  const handleResize = () => {
    // 延迟执行以确保DOM更新完成
    setTimeout(() => {
      debouncedUpdateScale();
    }, 50);
  };

  // 处理物资类型变化
  const handleMaterialChange = (value: string[], text: string) => {
    console.log('物资类型变化:', { value, text });
    // 这里可以根据选择的物资类型更新数据大屏的数据
    // 例如：重新获取对应物资类型的统计数据
  };

  // 处理省市区变化
  const handleAreaChange = (value: string[], text: string) => {
    console.log('省市区变化:', { value, text });
    // 这里可以根据选择的省市区更新数据大屏的数据
    // 例如：重新获取对应地区的统计数据
  };

  onMounted(async () => {
    // 开始性能监控
    performanceMonitor.mark('visual-init-start');

    // 优化的图片预加载策略
    try {
      // 关键背景图片 - 高优先级预加载
      const criticalImages = [
        'https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/visual-bg_1755478111715.png',
        'https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/header_1754987780270.png',
        'https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/center-bg_1754987648914.png',
      ];

      // 次要图片 - 普通优先级预加载
      const secondaryImages = [
        'https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/left-bg_1754987425208.png',
        'https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/right-bg_1754987518249.png',
        'https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/bottom-bg_1754987599965.png',
        '/src/assets/visual/head-card.png',
        '/src/assets/visual/address-card.png',
      ];

      // 小图标 - 低优先级预加载
      const iconImages = [
        '/src/assets/visual/center/icon1.png',
        '/src/assets/visual/center/icon2.png',
        '/src/assets/visual/center/icon3.png',
        '/src/assets/visual/center/icon4.png',
        '/src/assets/visual/center/icon5.png',
        '/src/assets/visual/medal-gold.png',
        '/src/assets/visual/medal-silver.png',
        '/src/assets/visual/medal-cuprum.png',
      ];

      // 分批预加载，避免阻塞主线程
      const preloadPromises = [
        imagePreloader.preloadImages(criticalImages, {
          priority: 'high',
          quality: 0.9,
          maxWidth: 1920,
          maxHeight: 1080,
          batchSize: 2,
          delay: 0,
        }),
        imagePreloader.preloadImages(secondaryImages, {
          priority: 'normal',
          quality: 0.8,
          maxWidth: 1920,
          maxHeight: 1080,
          batchSize: 2,
          delay: 100,
        }),
        imagePreloader.preloadImages(iconImages, {
          priority: 'low',
          quality: 0.7,
          maxWidth: 512,
          maxHeight: 512,
          batchSize: 3,
          delay: 200,
        }),
      ];

      // 并行预加载，但不等待全部完成
      Promise.allSettled(preloadPromises).then((results) => {
        const stats = imagePreloader.getCacheStats();
        console.log('图片预加载完成:', {
          成功加载: results.filter((r) => r.status === 'fulfilled').length,
          加载失败: results.filter((r) => r.status === 'rejected').length,
          缓存统计: stats,
        });
      });

      // 只等待关键图片加载完成
      await preloadPromises[0];
    } catch (error) {
      console.warn('关键图片预加载失败:', error);
    }

    // 检查是否需要自动进入全屏（通过URL参数控制）
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('fullscreen') === 'true') {
      enterFullscreen();
    }

    // 添加事件监听
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('msfullscreenchange', handleFullscreenChange);
    document.addEventListener('keydown', handleKeydown);
    window.addEventListener('resize', handleResize);

    // 设置ResizeObserver监听容器尺寸变化
    const container = document.querySelector('.visual-container') as HTMLElement;
    if (container && window.ResizeObserver) {
      resizeObserver.value = new ResizeObserver(() => {
        handleResize();
      });
      resizeObserver.value.observe(container);
    }

    // 初始化响应式适配
    setTimeout(() => {
      handleResize();

      // 结束性能监控
      performanceMonitor.mark('visual-init-end');
      const initTime = performanceMonitor.measure('visual-init', 'visual-init-start', 'visual-init-end');
      console.log(`数据可视化大屏初始化耗时: ${initTime.toFixed(2)}ms`);
    }, 100);
  });

  onUnmounted(() => {
    // 移除事件监听
    document.removeEventListener('fullscreenchange', handleFullscreenChange);
    document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.removeEventListener('msfullscreenchange', handleFullscreenChange);
    document.removeEventListener('keydown', handleKeydown);
    window.removeEventListener('resize', handleResize);

    // 清理ResizeObserver
    if (resizeObserver.value) {
      resizeObserver.value.disconnect();
      resizeObserver.value = null;
    }

    // 清理性能监控数据
    performanceMonitor.clear();

    // 清理图片缓存
    imagePreloader.clear();

    console.log('数据可视化大屏组件已卸载');
  });
</script>

<style lang="less" scoped>
  @import './styles/visual-common.less';

  .visual-container {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: calc(100vh - 86px); // 减去新的顶栏高度
    overflow: hidden;
    background: @bg-dark;
    display: flex;
    align-items: center;
    justify-content: center;

    &.fullscreen {
      // 全屏状态下保持正常布局，不隐藏侧边栏和顶栏
      // 只是标记状态，实际布局不变
      position: relative;
    }

    // 添加星空背景效果
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background:
        radial-gradient(2px 2px at 20px 30px, @primary-color, transparent),
        radial-gradient(2px 2px at 40px 70px, fade(@primary-color, 80%), transparent),
        radial-gradient(1px 1px at 90px 40px, fade(@primary-color, 60%), transparent),
        radial-gradient(1px 1px at 130px 80px, fade(@primary-color, 40%), transparent),
        radial-gradient(2px 2px at 160px 30px, fade(@primary-color, 70%), transparent);
      background-repeat: repeat;
      background-size: 200px 100px;
      animation: twinkle 4s ease-in-out infinite alternate;
      z-index: 0;
    }
  }

  @keyframes twinkle {
    0% {
      opacity: 0.3;
    }
    100% {
      opacity: 0.8;
    }
  }

  .visual-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/visual-bg_1755478111715.png') no-repeat center center;
    background-size: cover;
    z-index: 1;

    // 添加渐变遮罩
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, fade(@bg-dark, 20%) 0%, transparent 30%, transparent 70%, fade(@bg-dark, 20%) 100%);
    }
  }

  .visual-content {
    position: absolute;
    width: 1658px;
    height: 994px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
    transform-origin: center center;
    transition: transform 0.3s ease;
    display: flex;
    flex-direction: column;
  }

  .visual-header {
    height: 88px;
    width: 100%;
  }

  .visual-middle {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 577px;
    padding: 0 15px;
  }

  .visual-left {
    height: 577px;
    z-index: 1; /* 左侧区域在下层 */
    position: relative;
  }

  .visual-center {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    left: 0;
    width: 100%;
    gap: 20px;
    height: 577px;
    z-index: 3; /* 中间区域在上层，压在两侧背景图上方 */
    position: absolute;
    margin: 0 auto;
  }

  .center-top {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .center-bottom {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .visual-right {
    height: 577px;
    z-index: 1; /* 右侧区域在下层 */
    position: relative;
  }

  .visual-footer {
    height: 277px;
    padding: 12px 20px;
  }
</style>
