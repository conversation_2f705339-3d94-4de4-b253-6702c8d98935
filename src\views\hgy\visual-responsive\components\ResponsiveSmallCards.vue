<template>
  <div class="responsive-small-cards">
    <div class="cards-grid">
      <!-- 标的总量 -->
      <div class="small-card">
        <div class="card-content">
          <div class="card-icon">
            <FileTextOutlined />
          </div>
          <div class="card-info">
            <div class="card-label">标的总量</div>
            <div class="card-value">
              <CountTo :start-val="0" :end-val="smallCardsData.targetTotal" :duration="1500" suffix="个" />
            </div>
          </div>
        </div>
      </div>

      <!-- 资产处置总量 -->
      <div class="small-card">
        <div class="card-content">
          <div class="card-icon">
            <BankOutlined />
          </div>
          <div class="card-info">
            <div class="card-label">资产处置总量</div>
            <div class="card-value">
              <CountTo :start-val="0" :end-val="smallCardsData.assetTotal" :duration="1500" suffix="个" />
            </div>
          </div>
        </div>
      </div>

      <!-- 标的成交额 -->
      <div class="small-card">
        <div class="card-content">
          <div class="card-icon">
            <DollarCircleOutlined />
          </div>
          <div class="card-info">
            <div class="card-label">标的成交额</div>
            <div class="card-value">
              <CountTo :start-val="0" :end-val="smallCardsData.targetAmount" :duration="1500" :decimals="2" suffix="万" />
            </div>
          </div>
        </div>
      </div>

      <!-- 资产处置成交额 -->
      <div class="small-card">
        <div class="card-content">
          <div class="card-icon">
            <FundOutlined />
          </div>
          <div class="card-info">
            <div class="card-label">资产处置成交额</div>
            <div class="card-value">
              <CountTo :start-val="0" :end-val="smallCardsData.assetAmount" :duration="1500" :decimals="2" suffix="万" />
            </div>
          </div>
        </div>
      </div>

      <!-- 标的溢价率 -->
      <div class="small-card">
        <div class="card-content">
          <div class="card-icon">
            <RiseOutlined />
          </div>
          <div class="card-info">
            <div class="card-label">标的溢价率</div>
            <div class="card-value">
              <CountTo :start-val="0" :end-val="smallCardsData.targetPremiumRate" :duration="1500" :decimals="2" suffix="%" />
            </div>
          </div>
        </div>
      </div>

      <!-- 今日成交 -->
      <div class="small-card">
        <div class="card-content">
          <div class="card-icon">
            <CalendarOutlined />
          </div>
          <div class="card-info">
            <div class="card-label">今日成交</div>
            <div class="card-value">
              <CountTo :start-val="0" :end-val="smallCardsData.todayDeal" :duration="1500" suffix="笔" />
            </div>
          </div>
        </div>
      </div>

      <!-- 本月成交 -->
      <div class="small-card">
        <div class="card-content">
          <div class="card-icon">
            <BarChartOutlined />
          </div>
          <div class="card-info">
            <div class="card-label">本月成交</div>
            <div class="card-value">
              <CountTo :start-val="0" :end-val="smallCardsData.monthDeal" :duration="1500" suffix="笔" />
            </div>
          </div>
        </div>
      </div>

      <!-- 活跃用户 -->
      <div class="small-card">
        <div class="card-content">
          <div class="card-icon">
            <UserOutlined />
          </div>
          <div class="card-info">
            <div class="card-label">活跃用户</div>
            <div class="card-value">
              <CountTo :start-val="0" :end-val="smallCardsData.activeUsers" :duration="1500" suffix="人" />
            </div>
          </div>
        </div>
      </div>

      <!-- 在线用户 -->
      <div class="small-card">
        <div class="card-content">
          <div class="card-icon">
            <TeamOutlined />
          </div>
          <div class="card-info">
            <div class="card-label">在线用户</div>
            <div class="card-value">
              <CountTo :start-val="0" :end-val="smallCardsData.onlineUsers" :duration="1500" suffix="人" />
            </div>
          </div>
        </div>
      </div>

      <!-- 成功率 -->
      <div class="small-card">
        <div class="card-content">
          <div class="card-icon">
            <CheckCircleOutlined />
          </div>
          <div class="card-info">
            <div class="card-label">成功率</div>
            <div class="card-value">
              <CountTo :start-val="0" :end-val="smallCardsData.successRate" :duration="1500" :decimals="1" suffix="%" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, inject } from 'vue';
  import {
    FileTextOutlined,
    BankOutlined,
    DollarCircleOutlined,
    FundOutlined,
    RiseOutlined,
    CalendarOutlined,
    BarChartOutlined,
    UserOutlined,
    TeamOutlined,
    CheckCircleOutlined,
  } from '@ant-design/icons-vue';
  import CountTo from './CountTo.vue';

  // 注入数据服务
  const dataService = inject('dataService') as any;

  // 小卡片数据
  const smallCardsData = ref({
    targetTotal: 0,
    assetTotal: 0,
    targetAmount: 0,
    assetAmount: 0,
    targetPremiumRate: 0,
    todayDeal: 0,
    monthDeal: 0,
    activeUsers: 0,
    onlineUsers: 0,
    successRate: 0,
  });

  // 加载数据
  const loadData = async () => {
    try {
      // 模拟数据加载
      await new Promise((resolve) => setTimeout(resolve, 300));

      smallCardsData.value = {
        targetTotal: 1256,
        assetTotal: 892,
        targetAmount: 8567.89,
        assetAmount: 4012.34,
        targetPremiumRate: 28.5,
        todayDeal: 45,
        monthDeal: 1234,
        activeUsers: 5678,
        onlineUsers: 234,
        successRate: 95.8,
      };
    } catch (error) {
      console.error('加载小卡片数据失败:', error);
    }
  };

  onMounted(() => {
    loadData();
  });
</script>

<style lang="less" scoped>
  @import '../styles/responsive-variables.less';
  @import '../styles/responsive-mixins.less';

  .responsive-small-cards {
    width: 100%;
    padding: var(--spacing-md);
  }

  .cards-grid {
    .responsive-grid(5, var(--spacing-md));

    .respond-to(xs) {
      .content() {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-sm);
      }
    }

    .respond-to(sm) {
      .content() {
        grid-template-columns: repeat(3, 1fr);
      }
    }

    .mobile-first(md) {
      .content() {
        grid-template-columns: repeat(5, 1fr);
      }
    }

    .mobile-first(lg) {
      .content() {
        grid-template-columns: repeat(5, 1fr);
      }
    }
  }

  .small-card {
    .responsive-card-bg('');
    height: 80px;
    cursor: pointer;
    transition: all var(--duration-normal) ease;

    &:hover {
      transform: translateY(-2px);
      .glow-effect(#2bccff, 12px, 0.3);

      &::after {
        border-color: var(--primary-color);
      }
    }

    .respond-to(xs) {
      .content() {
        height: 70px;
      }
    }
  }

  .card-content {
    position: relative;
    z-index: 3;
    height: 100%;
    padding: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);

    .respond-to(xs) {
      .content() {
        padding: var(--spacing-xs);
        gap: var(--spacing-xs);
      }
    }
  }

  .card-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-color-20);
    border: 1px solid var(--primary-color);
    border-radius: 50%;
    color: var(--primary-color);
    font-size: 16px;
    flex-shrink: 0;
    .glow-effect(#2bccff, 6px, 0.3);

    .respond-to(xs) {
      .content() {
        width: 28px;
        height: 28px;
        font-size: 14px;
      }
    }
  }

  .card-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
    min-width: 0;
  }

  .card-label {
    .responsive-font-size(var(--font-xs));
    color: var(--text-secondary);
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    .respond-to(xs) {
      .content() {
        font-size: 10px;
      }
    }
  }

  .card-value {
    .responsive-font-size(var(--font-md));
    color: var(--text-light);
    font-weight: bold;
    .text-glow(#2bccff, 6px, 0.4);

    .respond-to(xs) {
      .content() {
        font-size: var(--font-sm);
      }
    }
  }

  // 响应式动画优化
  @media (prefers-reduced-motion: reduce) {
    .small-card {
      transition: none;

      &:hover {
        transform: none;
      }
    }
  }

  // 高对比度模式支持
  @media (prefers-contrast: high) {
    .small-card {
      &::after {
        border-width: 2px;
      }
    }

    .card-icon {
      border-width: 2px;
    }
  }
</style>
