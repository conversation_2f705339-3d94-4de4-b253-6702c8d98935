.bottom-charts {
  display: flex;
  height: 281px; /* PostCSS 转 vw */
  gap: 20px; /* PostCSS 转 vw */
}

.chart-card {
  position: relative;
  flex: 1;
  border-radius: 8px;
  overflow: hidden;
}

.card-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/bottom-bg_1754987599965.png') no-repeat center center;
  background-size: cover;
  z-index: 1;
}

.card-content {
  position: relative;
  height: 100%;
  padding: 7px 20px; /* 转 vw */
  z-index: 2;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  color: #fff;
  font-size: 22px; /* 转 vw */
  font-family: 'YouSheBiaoTiHei';
}

.card-controls {
  display: flex;
  align-items: center;
  gap: 20px; /* 转 vw */
}

.chart-legend {
  display: flex;
  align-items: center;
  gap: 16px; /* 转 vw */
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px; /* 转 vw */
}

.legend-icon {
  flex-shrink: 0;
}

.line-icon {
  position: relative;
  width: 16px; /* 转 vw */
  height: 12px; /* 转 vw */
  background: transparent;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 16px; /* 转 vw */
    height: 2px; /* 转 vw */
    background: #2bccff;
    transform: translateY(-50%);
  }

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 6px; /* 转 vw */
    height: 6px; /* 转 vw */
    background: #ffffff;
    border: 2px solid #2bccff;
    border-radius: 50%;
    transform: translate(-50%, -50%);
  }
}

.bar-icon {
  width: 28px; /* 转 vw */
  height: 11px; /* 转 vw */
  background: #2bccff;
  border-radius: 2px;
}

.legend-text {
  color: #ffffff;
  font-size: 12px; /* 转 vw */
  font-weight: 400;
}

.chart-filter {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 12px; /* 转 vw */
    width: 12px; /* 转 vw */
    height: 8px; /* 转 vw */
    background: url('@/assets/visual/center/down.png') no-repeat center center;
    background-size: contain;
    transform: translateY(-50%);
    pointer-events: none;
    z-index: 1;
  }
}

.filter-select {
  appearance: none;
  background: url('@/assets/visual/center/select-bg-mini.png') no-repeat center center;
  background-size: 100% 100%;
  border: none;
  outline: none;
  color: #2bccff;
  font-size: 12px; /* 转 vw */
  font-weight: 400;
  padding: 8px 32px 8px 16px; /* 转 vw */
  cursor: pointer;
  min-width: 90px; /* 转 vw */
  height: 32px; /* 转 vw */
  line-height: 16px; /* 转 vw */
  text-align: left;
  border-radius: 4px;
  transition: all 0.3s ease;

  &:hover {
    opacity: 0.8;
  }

  &:focus {
    box-shadow: 0 0 8px rgba(43, 204, 255, 0.3);
  }

  // 优化选项样式
  option {
    background: #0a4a52;
    color: #2bccff;
    padding: 8px 16px; /* 转 vw */
    border: none;
    font-size: 12px; /* 转 vw */
    font-weight: 400;

    &:hover {
      background: #0d5a64;
    }

    &:checked {
      background: #2bccff;
      color: #ffffff;
    }
  }
}

.ranking-grid {
  height: calc(100% - 40px);
  display: flex;
  flex-direction: column;
}

.top-row,
.bottom-row {
  display: flex;
  justify-content: space-around;
  flex: 1;
}

.ranking-medal {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  width: 161px; /* 转 vw */
  height: 115px; /* 转 vw */
}

.medal-bg-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('@/assets/visual/center/cj-bg.png') no-repeat center center;
  background-size: contain;
  z-index: 1;
}

.medal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 2;
  height: 100%;
  justify-content: center;
  gap: 6px;
}

.medal-icon {
  position: relative;
  width: 57.6px; /* 转 vw */
  height: 36px; /* 转 vw */
  flex-shrink: 0;
  margin-top: 13px; /* 转 vw */

  .medal-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    background-image: url('@/assets/visual/medal-regular.png');
  }

  .rank-number {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #004c66 !important;
    font-size: 11px;
    z-index: 2;
    font-family: 'DIN Bold';
  }
}

.ranking-medal.rank-1 .medal-icon .medal-bg {
  background-image: url('@/assets/visual/medal-gold.png');
}

.ranking-medal.rank-2 .medal-icon .medal-bg {
  background-image: url('@/assets/visual/medal-silver.png');
}

.ranking-medal.rank-3 .medal-icon .medal-bg {
  background-image: url('@/assets/visual/medal-cuprum.png');
}

.medal-info {
  text-align: center;
}

.medal-value {
  color: #fff;
  font-size: 22px; /* 转 vw */
  text-shadow: 0 0 8px rgba(18, 230, 219, 0.5);
  font-family: 'DIN Regular';
  .medal-unit {
    font-size: 14px; /* 转 vw */
    font-family: 'PingFang Medium';
  }
}

.medal-name {
  color: #ffffff;
  font-size: 14px; /* 转 vw */
  opacity: 0.8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 110px; /* 转 vw */
}

.chart-wrapper {
  height: calc(100% - 40px);
}

.chart {
  width: 100%;
  height: 100%;
}
