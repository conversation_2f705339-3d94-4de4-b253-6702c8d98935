# 消息收件箱页面

## 接口对接说明

### API接口
- **接口地址**: `/receive/hgyReceive/queryAllReceive`
- **请求方式**: POST
- **接口文件**: `src/views/messageCenter/Inbox/inbox.api.ts`

### 请求参数
```json
{
  "pageNo": 1,
  "pageSize": 10,
  "receiveUserId": "1955882868295077889",
  "keywords": "发"
}
```

#### 参数说明
- `pageNo`: 页码，从1开始
- `pageSize`: 每页数量，默认10
- `receiveUserId`: 接收用户ID，自动从用户store中获取
- `keywords`: 搜索关键词，可选

### 响应数据结构
```json
{
  "records": [
    {
      "id": "消息ID",
      "sender": "发送者",
      "productInfo": "关联产品信息",
      "content": "留言内容",
      "source": "消息来源",
      "sendTime": "发送时间",
      "status": "消息状态"
    }
  ],
  "total": 100,
  "size": 10,
  "current": 1,
  "pages": 10
}
```

## 功能特性

### 搜索功能
- **关键词搜索**: 支持按关键词搜索消息内容
- **消息来源筛选**: 网站留言、微信客服、电话咨询、邮件咨询
- **消息状态筛选**: 未读、已读、已回复
- **时间区间筛选**: 支持按时间范围查询

### 表格功能
- **分页显示**: 支持分页查询
- **列宽调整**: 支持拖拽调整列宽
- **操作按钮**: 回复、删除功能

### 数据处理
- **自动获取用户ID**: 从用户store中自动获取当前用户ID
- **错误处理**: 接口调用失败时显示错误提示
- **参数转换**: 自动处理分页参数转换

## 技术实现

### 文件结构
```
src/views/messageCenter/Inbox/
├── index.vue          # 主页面组件
├── inbox.api.ts       # API接口定义
└── README.md          # 说明文档
```

### 关键代码

#### API调用
```typescript
const fetchData = async (params: any) => {
  const requestParams = {
    pageNo: params.page || 1,
    pageSize: params.pageSize || 10,
    receiveUserId: String(userStore.getUserInfo?.id || ''),
    keywords: params.keywords || '',
  };

  const result = await queryAllReceive(requestParams);
  return {
    items: result.records || [],
    total: result.total || 0,
  };
};
```

#### 用户ID获取
```typescript
import { useUserStore } from '/@/store/modules/user';
const userStore = useUserStore();
```

## 注意事项

1. **用户ID**: 接口需要当前登录用户的ID，确保用户已登录
2. **权限控制**: 只能查看当前用户的收件箱消息
3. **错误处理**: 接口调用失败时会显示错误提示
4. **数据格式**: 确保后端返回的数据格式与前端期望一致

## 待开发功能

- [ ] 回复留言功能的具体实现
- [ ] 删除留言的后端接口对接
- [ ] 消息状态更新（标记已读/未读）
- [ ] 批量操作功能
