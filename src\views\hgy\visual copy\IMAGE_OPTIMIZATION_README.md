# 数据大屏图片优化方案

## 问题描述

数据大屏页面使用了大量本地图片资源，首次加载时图片加载缓慢，影响用户体验。

## 优化方案

### 1. 图片预加载优化

#### 分级预加载策略

- **关键图片（高优先级）**：主背景、头部背景等核心视觉元素
- **次要图片（普通优先级）**：左右侧背景、底部背景等
- **装饰图片（低优先级）**：小图标、奖牌图标等

#### 实现特点

- 批量预加载，避免阻塞主线程
- 支持图片压缩和格式转换（WebP/JPEG）
- 智能缓存管理，避免重复加载
- 性能监控和统计

### 2. 图片压缩优化

#### 自动压缩

- 根据图片用途自动选择压缩质量
- 支持WebP格式（现代浏览器）
- 自动计算合适的尺寸

#### 压缩配置

```typescript
// 关键背景图片
{
  quality: 0.9,        // 高质量
  maxWidth: 1920,      // 最大宽度
  maxHeight: 1080,     // 最大高度
}

// 装饰图片
{
  quality: 0.7,        // 较低质量
  maxWidth: 512,       // 较小尺寸
  maxHeight: 512,
}
```

### 3. 懒加载和渐进式加载

#### 懒加载特性

- 基于Intersection Observer API
- 支持占位符显示
- 渐进式图片替换

#### 使用方法

```typescript
import { progressiveImageLoader } from './utils/imageOptimizer';

// 创建渐进式加载的图片
const img = progressiveImageLoader.createProgressiveImage(container, '/src/assets/visual/background.png', placeholderSrc, {
  quality: 0.8,
  maxWidth: 1920,
  maxHeight: 1080,
  lazy: true,
});
```

### 4. CSS优化

#### 硬件加速

```less
.optimized-bg(@bg-image) {
  background: url(@bg-image) no-repeat center center;
  background-size: cover;
  // 启用硬件加速
  transform: translateZ(0);
  will-change: transform;
  // 图片渲染优化
  image-rendering: -webkit-optimize-contrast;
}
```

#### 懒加载样式

```less
.lazy-bg(@bg-image, @placeholder-color) {
  background: @placeholder-color;
  transition: background-image 0.3s ease;

  &.loaded {
    background-image: url(@bg-image);
  }

  &.loading {
    // 显示加载动画
  }
}
```

## 使用指南

### 1. 基本使用

```typescript
import { imagePreloader } from './utils/performance';

// 预加载关键图片
await imagePreloader.preloadImages(
  [
    'https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/visual-bg_1755478111715.png',
    'https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/header_1754987780270.png',
  ],
  {
    priority: 'high',
    quality: 0.9,
    maxWidth: 1920,
    maxHeight: 1080,
  }
);
```

### 2. 高级使用

```typescript
import { ImageOptimizer, ProgressiveImageLoader, ImagePreloadManager } from './utils/imageOptimizer';

// 创建优化器实例
const optimizer = new ImageOptimizer();

// 压缩图片
const compressedSrc = await optimizer.compressImage(
  '/src/assets/visual/large-image.png',
  0.8, // 质量
  1920, // 最大宽度
  1080 // 最大高度
);

// 渐进式加载
const loader = new ProgressiveImageLoader();
const img = loader.createProgressiveImage(document.getElementById('container'), '/src/assets/visual/background.png');
```

### 3. 性能监控

```typescript
import { imageLoadingMonitor } from './utils/buildOptimization';

// 获取加载统计
const stats = imageLoadingMonitor.getStats();
console.log('图片加载统计:', {
  加载中: stats.loading,
  已加载: stats.loaded,
  加载失败: stats.failed,
  成功率: stats.successRate + '%',
});
```

## 优化效果

### 预期改进

1. **首次加载时间**：减少50-70%
2. **内存使用**：减少30-50%
3. **网络传输**：减少40-60%
4. **用户体验**：显著提升

### 性能指标

- 关键图片加载时间：< 500ms
- 总体页面加载时间：< 2s
- 图片压缩率：40-60%
- 内存使用优化：30-50%

## 最佳实践

### 1. 图片资源管理

- 按优先级组织图片资源
- 使用合适的图片格式（WebP > JPEG > PNG）
- 控制图片尺寸和质量

### 2. 预加载策略

- 优先加载关键视觉元素
- 分批加载，避免阻塞
- 合理设置缓存策略

### 3. 性能监控

- 监控图片加载时间
- 跟踪内存使用情况
- 分析用户体验指标

## 注意事项

### 1. 浏览器兼容性

- WebP格式需要现代浏览器支持
- Intersection Observer API需要polyfill（IE）
- 硬件加速在某些设备上可能有问题

### 2. 内存管理

- 及时清理不需要的图片缓存
- 监控内存使用情况
- 避免同时加载过多大图片

### 3. 网络环境

- 考虑弱网环境下的加载策略
- 提供降级方案
- 支持离线缓存

## 构建优化

### Vite配置建议

```typescript
// vite.config.ts
export default {
  build: {
    rollupOptions: {
      output: {
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split('.');
          const ext = info[info.length - 1];
          if (/\.(png|jpe?g|svg|gif|tiff|bmp|ico)$/i.test(assetInfo.name)) {
            return `images/[name]-[hash][extname]`;
          }
          return `assets/[name]-[hash][extname]`;
        },
      },
    },
  },
  // 图片优化插件
  plugins: [
    // 可以添加图片压缩插件
  ],
};
```

### CDN部署建议

- 将图片资源部署到CDN
- 启用HTTP/2和压缩
- 配置合适的缓存策略
- 使用多个域名并行加载

## 总结

通过实施这套图片优化方案，数据大屏页面的加载性能将得到显著提升，用户体验明显改善。建议根据实际情况调整优化参数，并持续监控性能指标。
