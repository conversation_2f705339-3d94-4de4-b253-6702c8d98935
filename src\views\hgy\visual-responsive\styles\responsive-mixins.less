// 响应式数据可视化大屏 - 混合器和工具类

// 响应式断点混合器
.respond-to(@breakpoint) when (@breakpoint = xs) {
  @media (max-width: (@screen-sm - 1px)) { & { .content(); } }
}
.respond-to(@breakpoint) when (@breakpoint = sm) {
  @media (min-width: @screen-sm) and (max-width: (@screen-md - 1px)) { & { .content(); } }
}
.respond-to(@breakpoint) when (@breakpoint = md) {
  @media (min-width: @screen-md) and (max-width: (@screen-lg - 1px)) { & { .content(); } }
}
.respond-to(@breakpoint) when (@breakpoint = lg) {
  @media (min-width: @screen-lg) and (max-width: (@screen-xl - 1px)) { & { .content(); } }
}
.respond-to(@breakpoint) when (@breakpoint = xl) {
  @media (min-width: @screen-xl) and (max-width: (@screen-2xl - 1px)) { & { .content(); } }
}
.respond-to(@breakpoint) when (@breakpoint = 2xl) {
  @media (min-width: @screen-2xl) { & { .content(); } }
}

// 移动端优先的响应式混合器
.mobile-first(@breakpoint) when (@breakpoint = sm) {
  @media (min-width: @screen-sm) { & { .content(); } }
}
.mobile-first(@breakpoint) when (@breakpoint = md) {
  @media (min-width: @screen-md) { & { .content(); } }
}
.mobile-first(@breakpoint) when (@breakpoint = lg) {
  @media (min-width: @screen-lg) { & { .content(); } }
}
.mobile-first(@breakpoint) when (@breakpoint = xl) {
  @media (min-width: @screen-xl) { & { .content(); } }
}
.mobile-first(@breakpoint) when (@breakpoint = 2xl) {
  @media (min-width: @screen-2xl) { & { .content(); } }
}

// 桌面端优先的响应式混合器
.desktop-first(@breakpoint) when (@breakpoint = xl) {
  @media (max-width: @screen-xl) { & { .content(); } }
}
.desktop-first(@breakpoint) when (@breakpoint = lg) {
  @media (max-width: @screen-lg) { & { .content(); } }
}
.desktop-first(@breakpoint) when (@breakpoint = md) {
  @media (max-width: @screen-md) { & { .content(); } }
}
.desktop-first(@breakpoint) when (@breakpoint = sm) {
  @media (max-width: @screen-sm) { & { .content(); } }
}

// 发光效果混合器
.glow-effect(@color: var(--primary-color), @blur: 10px, @opacity: 0.5) {
  box-shadow: 0 0 @blur fade(@color, @opacity * 100%);
}

.text-glow(@color: var(--primary-color), @blur: 8px, @opacity: 0.6) {
  text-shadow: 0 0 @blur fade(@color, @opacity * 100%);
}

// 响应式卡片背景混合器
.responsive-card-bg(@bg-image, @border-opacity: 0.3, @bg-opacity: 0.05) {
  position: relative;
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: var(--card-radius);
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url(@bg-image) no-repeat center center;
    background-size: cover;
    opacity: 0.1;
    z-index: 1;
  }
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-bg);
    border: 1px solid fade(var(--primary-color), @border-opacity * 100%);
    border-radius: var(--card-radius);
    z-index: 2;
  }
  
  .card-content {
    position: relative;
    z-index: 3;
    padding: var(--card-padding);
  }
}

// 响应式网格混合器
.responsive-grid(@columns: 1, @gap: var(--spacing-md)) {
  display: grid;
  grid-template-columns: repeat(@columns, 1fr);
  gap: @gap;
  
  .mobile-first(sm) {
    .content() {
      grid-template-columns: repeat(min(@columns * 2, 12), 1fr);
    }
  }
  
  .mobile-first(md) {
    .content() {
      grid-template-columns: repeat(min(@columns * 3, 12), 1fr);
    }
  }
  
  .mobile-first(lg) {
    .content() {
      grid-template-columns: repeat(@columns, 1fr);
    }
  }
}

// 响应式Flexbox混合器
.responsive-flex(@direction: row, @wrap: wrap, @justify: flex-start, @align: stretch) {
  display: flex;
  flex-direction: @direction;
  flex-wrap: @wrap;
  justify-content: @justify;
  align-items: @align;
  
  .respond-to(xs) {
    .content() {
      flex-direction: column;
      align-items: stretch;
    }
  }
}

// 响应式字体大小混合器
.responsive-font-size(@base-size: var(--font-md)) {
  font-size: @base-size;
  
  .respond-to(xs) {
    .content() {
      font-size: calc(@base-size * 0.8);
    }
  }
  
  .respond-to(sm) {
    .content() {
      font-size: calc(@base-size * 0.9);
    }
  }
  
  .mobile-first(lg) {
    .content() {
      font-size: calc(@base-size * 1.1);
    }
  }
  
  .mobile-first(xl) {
    .content() {
      font-size: calc(@base-size * 1.2);
    }
  }
}

// 响应式间距混合器
.responsive-spacing(@property: padding, @base-size: var(--spacing-md)) {
  @{property}: @base-size;
  
  .respond-to(xs) {
    .content() {
      @{property}: calc(@base-size * 0.5);
    }
  }
  
  .respond-to(sm) {
    .content() {
      @{property}: calc(@base-size * 0.75);
    }
  }
  
  .mobile-first(lg) {
    .content() {
      @{property}: calc(@base-size * 1.25);
    }
  }
  
  .mobile-first(xl) {
    .content() {
      @{property}: calc(@base-size * 1.5);
    }
  }
}

// 响应式容器混合器
.responsive-container(@max-width: @container-xl) {
  width: 100%;
  max-width: @max-width;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
  
  .respond-to(xs) {
    .content() {
      padding: 0 var(--spacing-sm);
    }
  }
  
  .mobile-first(lg) {
    .content() {
      padding: 0 var(--spacing-lg);
    }
  }
}

// 响应式图表容器混合器
.responsive-chart-container() {
  .responsive-card-bg('');
  height: var(--chart-height);
  
  .chart-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 3;
  }
  
  .chart-title {
    .responsive-font-size(var(--font-lg));
    color: var(--text-light);
    text-align: center;
    margin-bottom: var(--spacing-md);
    .text-glow();
  }
  
  .respond-to(xs) {
    .content() {
      height: calc(var(--chart-height) * 0.7);
    }
  }
  
  .respond-to(sm) {
    .content() {
      height: calc(var(--chart-height) * 0.8);
    }
  }
}

// 响应式排名列表混合器
.responsive-ranking-list() {
  .rank-item {
    display: flex;
    align-items: center;
    height: var(--rank-item-height);
    padding: var(--rank-item-padding);
    margin-bottom: var(--spacing-xs);
    background: var(--bg-dark-40);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    transition: all var(--duration-normal) ease;
    
    &:hover {
      background: var(--primary-color-10);
      border-color: var(--primary-color-40);
      transform: translateX(4px);
    }
    
    .rank-number {
      .responsive-font-size(var(--font-sm));
      color: var(--primary-color);
      font-weight: bold;
      margin-right: var(--spacing-sm);
      min-width: 24px;
      text-align: center;
    }
    
    .rank-name {
      .responsive-font-size(var(--font-sm));
      color: var(--text-light);
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .rank-value {
      .responsive-font-size(var(--font-sm));
      color: var(--primary-color);
      font-weight: bold;
      .text-glow();
    }
  }
  
  .respond-to(xs) {
    .content() {
      .rank-item {
        height: calc(var(--rank-item-height) * 0.8);
        padding: calc(var(--rank-item-padding) * 0.8);
      }
    }
  }
}

// 响应式动画混合器
.responsive-animation(@name, @duration: var(--duration-normal), @timing: ease) {
  animation: @name @duration @timing;
  
  .respond-to(xs) {
    .content() {
      animation-duration: calc(@duration * 0.7);
    }
  }
  
  @media (prefers-reduced-motion: reduce) {
    animation: none;
  }
}

// 工具类
.visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}
