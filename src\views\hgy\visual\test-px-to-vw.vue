<template>
  <div class="test-container">
    <div class="test-box">
      <h1>测试 px 转 vw</h1>
      <p>这个盒子应该是 200px 宽度，会被转换为 vw</p>
    </div>
  </div>
</template>

<style lang="less" scoped>
  .test-container {
    width: 100%;
    height: 100vh;
    background: #000;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .test-box {
    width: 200px;
    height: 100px;
    background: #2bccff;
    border-radius: 8px;
    padding: 20px;
    font-size: 16px;
    color: white;
    text-align: center;
  }
</style>
