<template>
  <div class="p-4">
    <BasicTable @register="registerTable">
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              label: '回复',
              icon: 'ant-design:message-outlined',
              onClick: handleReply.bind(null, record),
            },
            {
              label: '删除',
              icon: 'ant-design:delete-outlined',
              color: 'error',
              popConfirm: {
                title: '确定要删除这条留言吗？',
                confirm: handleDelete.bind(null, record),
              },
            },
          ]"
        />
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { BasicColumn } from '/@/components/Table';
  import { FormSchema } from '/@/components/Form';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useUserStore } from '/@/store/modules/user';
  import { queryAllReceive } from './inbox.api';

  const { createMessage } = useMessage();
  const userStore = useUserStore();

  // 表格列配置
  const columns: BasicColumn[] = [
    {
      title: '发送者',
      dataIndex: 'sender',
      width: 120,
      resizable: true,
    },
    {
      title: '关联产品信息',
      dataIndex: 'productInfo',
      width: 200,
      resizable: true,
    },
    {
      title: '留言内容',
      dataIndex: 'content',
      width: 300,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '消息来源',
      dataIndex: 'source',
      width: 120,
      resizable: true,
    },
    {
      title: '发送时间',
      dataIndex: 'sendTime',
      width: 180,
      resizable: true,
    },
  ];

  // 搜索表单配置
  const searchFormSchema: FormSchema[] = [
    {
      field: 'keywords',
      label: '关键词',
      component: 'Input',
      componentProps: {
        placeholder: '请输入关键词搜索',
      },
      colProps: { span: 6 },
    },
    {
      field: 'source',
      label: '消息来源',
      component: 'Select',
      componentProps: {
        placeholder: '请选择消息来源',
        options: [
          { label: '网站留言', value: 'website' },
          { label: '微信客服', value: 'wechat' },
          { label: '电话咨询', value: 'phone' },
          { label: '邮件咨询', value: 'email' },
        ],
      },
      colProps: { span: 6 },
    },
    {
      field: 'status',
      label: '消息状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择消息状态',
        options: [
          { label: '未读', value: 'unread' },
          { label: '已读', value: 'read' },
          { label: '已回复', value: 'replied' },
        ],
      },
      colProps: { span: 6 },
    },
    {
      field: 'timeRange',
      label: '时间区间',
      component: 'RangePicker',
      componentProps: {
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
        placeholder: ['开始时间', '结束时间'],
      },
      colProps: { span: 6 },
    },
  ];

  // API请求函数
  const fetchData = async (params: any) => {
    console.log('查询参数:', params);

    // 构建请求参数
    const requestParams = {
      pageNo: params.page || 1,
      pageSize: params.pageSize || 10,
      receiveUserId: String(userStore.getUserInfo?.id || ''),
      keywords: params.keywords || '收',
    };

    try {
      const result = await queryAllReceive(requestParams);

      return {
        items: result.records || [],
        total: result.total || 0,
      };
    } catch (error) {
      console.error('查询收件箱失败:', error);
      return {
        items: [],
        total: 0,
      };
    }
  };

  // 表格配置
  const [registerTable, { reload }] = useTable({
    api: fetchData,
    columns,
    striped: false,
    useSearchForm: true,
    showTableSetting: false,
    bordered: false,
    showIndexColumn: false,
    canResize: true,
    inset: true,
    maxHeight: 478,
    actionColumn: {
      width: 160,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      fixed: 'right',
    },
    rowKey: 'id',
    formConfig: {
      labelWidth: 64,
      size: 'large',
      schemas: searchFormSchema,
      actionColOptions: {
        span: 6,
        style: {
          textAlign: 'left',
        },
      },
    },
  });

  // 回复留言
  function handleReply(record: any) {
    console.log('回复留言:', record);
    createMessage.info(`回复给 ${record.sender} 的留言功能待开发`);
  }

  // 删除留言
  function handleDelete(record: any) {
    console.log('删除留言:', record);
    createMessage.success(`已删除 ${record.sender} 的留言`);
    reload();
  }
</script>

<style lang="less" scoped>
  .p-4 {
    padding: 0;
    :deep(.ant-pagination) {
      margin-bottom: -24px !important;
    }
    :deep(.ant-form) {
      padding: 0;
    }
  }

  :deep(.ant-form-item-control-input-content) {
    button {
      margin-right: 0;
      margin-left: 8px;
      box-shadow: 0 0 0 rgba(3, 38, 43, 0.42);
    }
  }
</style>
