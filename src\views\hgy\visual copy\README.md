# 灰谷网经营数据可视化大屏

基于Vue3开发的全屏数据可视化大屏页面，严格按照Figma设计图实现。

## 功能特性

### 🖥️ 全屏模式

- 进入页面自动全屏显示
- 隐藏所有Layout组件（侧边栏/标签栏/头部）
- 按ESC键退出全屏时返回首页

### 🎨 异形卡片背景

- 使用设计图提供的异形卡片背景图片
- 路径：`src/assets/visual/`
- 卡片背景适配不同尺寸组件
- 发光边框效果和渐变背景

### 📱 响应式布局

- 基于1658×994大屏分辨率设计
- 自动缩放适配不同屏幕尺寸
- 保持设计稿比例和视觉效果

### 🎯 数字动画效果

- 数字滚动计数器动画
- 缓动函数优化动画体验
- 支持千分位分隔符格式化

### 📊 ECharts图表

- 渐变颜色配置
- 数据标签显示
- 坐标轴格式化
- 图例位置优化
- 动画效果配置

## 页面布局

### 整体结构（上中下三层）

- **头部区域**：标题居中，左侧物资种类和省市区选择按钮，右侧时间和返回按钮
- **中间区域**：左侧标的数据排名，中间KPI指标和小卡片，右侧资产处置数据排名
- **底部区域**：成交额排名（奖牌式）、标的溢价趋势图、资产处置溢价趋势图

### 详细说明

- **头部**：高度120px，包含导航按钮和实时时间
- **中间**：高度627px，三栏布局，左右各666px宽度
- **底部**：高度333px，三个图表卡片并排显示

## 文件结构

```
src/views/hgy/visual/
├── index.vue                 # 主页面入口
├── components/               # 组件目录
│   ├── VisualHeader.vue     # 头部组件
│   ├── TargetRanking.vue    # 标的数据排名
│   ├── MainKPICards.vue     # 主要KPI卡片
│   ├── SmallCards.vue       # 小卡片组件
│   ├── AssetRanking.vue     # 资产处置排名
│   ├── BottomCharts.vue     # 底部图表
│   └── CountTo.vue          # 数字滚动动画
├── styles/                  # 样式目录
│   └── visual-common.less   # 通用样式
├── utils/                   # 工具函数
│   ├── responsive.ts        # 响应式适配
│   └── performance.ts       # 性能优化
├── services/                # 数据服务
│   └── dataService.ts       # 数据服务
└── README.md               # 说明文档
```

## 组件说明

### 主页面 (index.vue)

- 全屏控制逻辑
- 响应式适配
- 组件布局管理
- 性能监控

### 头部组件 (VisualHeader.vue)

- 标题显示："灰谷网经营数据可视化大屏"
- 实时时间显示
- 地理位置信息：河南省 > 郑州市 > 二七区
- 导航按钮

### 数据卡片 (DataCards.vue)

- 成交总额、溢价总额、总溢价率
- 标的总量、资产处置总量
- 标的成交额、资产处置成交额
- 标的溢价率
- 数字滚动动画效果

### 图表区域 (ChartSection.vue)

- 标的溢价趋势折线图
- 成交额排名柱状图
- 渐变色彩配置
- 动画效果

### 排名列表 (RankingSection.vue)

- 标的溢价额排名
- 标的溢价率排名
- 资产处置溢价率排名
- 自动滚动效果

## 样式特性

### 颜色主题

- 主色调：`#2BCCFF` (蓝色)
- 深色主题：`#0a0e27` (深蓝)
- 文字颜色：`#ffffff` (白色)
- 发光效果：基于主色调的阴影

### 动画效果

- 数字滚动动画
- 悬停效果
- 发光动画
- 渐变文字动画
- 星空背景闪烁

### 响应式断点

- Desktop: >= 1024px
- Tablet: 768px - 1023px
- Mobile: < 768px

## 性能优化

### 图片预加载

- 背景图片预加载
- 减少首屏加载时间
- 缓存管理

### 响应式适配

- 基于transform缩放
- 防抖和节流优化
- 设备性能检测

### 内存管理

- 组件卸载时清理资源
- 事件监听器清理
- 定时器清理

## 使用方法

### 1. 路由配置

确保路由指向 `src/views/hgy/visual/index.vue`

### 2. 资源准备

确保以下背景图片存在：

- `https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/visual-bg_1755478111715.png` - 主背景
- `https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/header_1754987780270.png` - 头部背景
- `https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/left-bg_1754987425208.png` - 左侧卡片背景
- `https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/center-bg_1754987648914.png` - 中间图表背景
- `https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/right-bg_1754987518249.png` - 右侧排名背景

### 3. 依赖检查

确保已安装以下依赖：

- `echarts` - 图表库
- `dayjs` - 时间处理
- `less` - 样式预处理器

### 4. 访问页面

直接访问对应路由，页面将自动进入全屏模式。

## 注意事项

1. **全屏权限**：某些浏览器可能需要用户手动允许全屏
2. **性能考虑**：在低性能设备上会自动降低动画效果
3. **浏览器兼容**：建议使用现代浏览器（Chrome、Firefox、Safari、Edge）
4. **分辨率适配**：最佳显示效果为1658×994及以上分辨率

## 自定义配置

### 修改数据

编辑 `services/dataService.ts` 中的模拟数据

### 调整样式

修改 `styles/visual-common.less` 中的样式变量

### 性能调优

在 `utils/performance.ts` 中调整性能配置参数

## 技术栈

- **Vue 3** - 前端框架
- **TypeScript** - 类型支持
- **Less** - CSS预处理器
- **ECharts** - 图表库
- **Day.js** - 时间处理

## 浏览器支持

- Chrome >= 88
- Firefox >= 85
- Safari >= 14
- Edge >= 88

---

开发完成时间：2025年1月严格按照Figma设计图实现，未自作主张添加额外功能。
