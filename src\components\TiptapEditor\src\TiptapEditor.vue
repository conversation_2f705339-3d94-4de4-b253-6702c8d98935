<template>
  <div class="tiptap-editor" :class="{ 'tiptap-editor--disabled': disabled }">
    <!-- 工具栏 -->
    <div v-if="!hideToolbar" class="tiptap-toolbar">
      <!-- 字体设置 -->
      <div class="toolbar-group">
        <a-dropdown :trigger="['click']">
          <button type="button" class="toolbar-btn toolbar-btn--dropdown" title="字体">
            <span>{{ getCurrentFontFamily() }}</span>
            <DownOutlined />
          </button>
          <template #overlay>
            <a-menu @click="handleFontFamilyClick">
              <a-menu-item key="default">默认字体</a-menu-item>
              <a-menu-divider />
              <a-menu-item-group title="系统字体">
                <a-menu-item key="SimSun">宋体</a-menu-item>
                <a-menu-item key="SimHei">黑体</a-menu-item>
                <a-menu-item key="Microsoft YaHei">微软雅黑</a-menu-item>
                <a-menu-item key="KaiTi">楷体</a-menu-item>
                <a-menu-item key="FangSong">仿宋</a-menu-item>
              </a-menu-item-group>
              <a-menu-divider />
              <a-menu-item-group title="苹方字体">
                <a-menu-item key="PingFang Regular">苹方常规</a-menu-item>
                <a-menu-item key="PingFang Medium">苹方中等</a-menu-item>
                <a-menu-item key="PingFang Bold">苹方加粗</a-menu-item>
                <a-menu-item key="PingFang Heavy">苹方特粗</a-menu-item>
              </a-menu-item-group>
              <a-menu-divider />
              <a-menu-item-group title="DIN字体">
                <a-menu-item key="DIN Regular">DIN常规</a-menu-item>
                <a-menu-item key="DIN Bold">DIN加粗</a-menu-item>
                <a-menu-item key="DIN BlackItalic">DIN黑斜体</a-menu-item>
              </a-menu-item-group>
              <a-menu-divider />
              <a-menu-item-group title="特殊字体">
                <a-menu-item key="FZZongYi-M05S">方正综艺</a-menu-item>
                <a-menu-item key="YouSheBiaoTiHei">优设标题黑</a-menu-item>
              </a-menu-item-group>
              <a-menu-divider />
              <a-menu-item-group title="英文字体">
                <a-menu-item key="Arial">Arial</a-menu-item>
                <a-menu-item key="Times New Roman">Times New Roman</a-menu-item>
                <a-menu-item key="Courier New">Courier New</a-menu-item>
              </a-menu-item-group>
            </a-menu>
          </template>
        </a-dropdown>

        <a-dropdown :trigger="['click']">
          <button type="button" class="toolbar-btn toolbar-btn--dropdown" title="字号">
            <span>{{ getCurrentFontSize() }}</span>
            <DownOutlined />
          </button>
          <template #overlay>
            <a-menu @click="handleFontSizeClick">
              <a-menu-item key="12px">12px</a-menu-item>
              <a-menu-item key="14px">14px</a-menu-item>
              <a-menu-item key="16px">16px</a-menu-item>
              <a-menu-item key="18px">18px</a-menu-item>
              <a-menu-item key="20px">20px</a-menu-item>
              <a-menu-item key="24px">24px</a-menu-item>
              <a-menu-item key="28px">28px</a-menu-item>
              <a-menu-item key="32px">32px</a-menu-item>
              <a-menu-item key="36px">36px</a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>

      <!-- 分隔符 -->
      <div class="toolbar-divider"></div>

      <!-- 基础格式化 -->
      <div class="toolbar-group">
        <button
          type="button"
          class="toolbar-btn"
          :class="{ 'is-active': editor?.isActive('bold') }"
          @click="editor?.chain().focus().toggleBold().run()"
          title="粗体"
        >
          <BoldOutlined />
        </button>
        <button
          type="button"
          class="toolbar-btn"
          :class="{ 'is-active': editor?.isActive('italic') }"
          @click="editor?.chain().focus().toggleItalic().run()"
          title="斜体"
        >
          <ItalicOutlined />
        </button>
        <button
          type="button"
          class="toolbar-btn"
          :class="{ 'is-active': editor?.isActive('underline') }"
          @click="editor?.chain().focus().toggleUnderline().run()"
          title="下划线"
        >
          <UnderlineOutlined />
        </button>
        <button
          type="button"
          class="toolbar-btn"
          :class="{ 'is-active': editor?.isActive('strike') }"
          @click="editor?.chain().focus().toggleStrike().run()"
          title="删除线"
        >
          <StrikethroughOutlined />
        </button>
      </div>

      <!-- 分隔符 -->
      <div class="toolbar-divider"></div>

      <!-- 文字颜色和背景色 -->
      <div class="toolbar-group">
        <a-dropdown :trigger="['click']">
          <button type="button" class="toolbar-btn" title="文字颜色">
            <FontColorsOutlined />
            <div class="color-indicator" :style="{ backgroundColor: getCurrentTextColor() }"></div>
          </button>
          <template #overlay>
            <div class="color-picker">
              <div class="color-row">
                <div
                  v-for="color in textColors"
                  :key="color"
                  class="color-item"
                  :style="{ backgroundColor: color }"
                  @click="setTextColor(color)"
                  :title="color"
                ></div>
              </div>
              <div class="color-row">
                <button class="color-clear-btn" @click="clearTextColor()">清除颜色</button>
              </div>
            </div>
          </template>
        </a-dropdown>

        <a-dropdown :trigger="['click']">
          <button type="button" class="toolbar-btn" title="背景颜色">
            <BgColorsOutlined />
            <div class="color-indicator" :style="{ backgroundColor: getCurrentHighlightColor() }"></div>
          </button>
          <template #overlay>
            <div class="color-picker">
              <div class="color-row">
                <div
                  v-for="color in highlightColors"
                  :key="color"
                  class="color-item"
                  :style="{ backgroundColor: color }"
                  @click="setHighlightColor(color)"
                  :title="color"
                ></div>
              </div>
              <div class="color-row">
                <button class="color-clear-btn" @click="clearHighlightColor()">清除背景</button>
              </div>
            </div>
          </template>
        </a-dropdown>
      </div>

      <!-- 分隔符 -->
      <div class="toolbar-divider"></div>

      <!-- 标题 -->
      <div class="toolbar-group">
        <a-dropdown :trigger="['click']">
          <button type="button" class="toolbar-btn toolbar-btn--dropdown" title="标题">
            <span>{{ getHeadingText() }}</span>
            <DownOutlined />
          </button>
          <template #overlay>
            <a-menu @click="handleHeadingClick">
              <a-menu-item key="paragraph">正文</a-menu-item>
              <a-menu-item key="1">标题 1</a-menu-item>
              <a-menu-item key="2">标题 2</a-menu-item>
              <a-menu-item key="3">标题 3</a-menu-item>
              <a-menu-item key="4">标题 4</a-menu-item>
              <a-menu-item key="5">标题 5</a-menu-item>
              <a-menu-item key="6">标题 6</a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>

      <!-- 分隔符 -->
      <div class="toolbar-divider"></div>

      <!-- 对齐 -->
      <div class="toolbar-group">
        <button
          type="button"
          class="toolbar-btn"
          :class="{ 'is-active': editor?.isActive({ textAlign: 'left' }) }"
          @click="editor?.chain().focus().setTextAlign('left').run()"
          title="左对齐"
        >
          <AlignLeftOutlined />
        </button>
        <button
          type="button"
          class="toolbar-btn"
          :class="{ 'is-active': editor?.isActive({ textAlign: 'center' }) }"
          @click="editor?.chain().focus().setTextAlign('center').run()"
          title="居中对齐"
        >
          <AlignCenterOutlined />
        </button>
        <button
          type="button"
          class="toolbar-btn"
          :class="{ 'is-active': editor?.isActive({ textAlign: 'right' }) }"
          @click="editor?.chain().focus().setTextAlign('right').run()"
          title="右对齐"
        >
          <AlignRightOutlined />
        </button>
        <button
          type="button"
          class="toolbar-btn"
          :class="{ 'is-active': editor?.isActive({ textAlign: 'justify' }) }"
          @click="editor?.chain().focus().setTextAlign('justify').run()"
          title="两端对齐"
        >
          <MenuOutlined />
        </button>
      </div>

      <!-- 分隔符 -->
      <div class="toolbar-divider"></div>

      <!-- 列表 -->
      <div class="toolbar-group">
        <button
          type="button"
          class="toolbar-btn"
          :class="{ 'is-active': editor?.isActive('bulletList') }"
          @click="editor?.chain().focus().toggleBulletList().run()"
          title="无序列表"
        >
          <UnorderedListOutlined />
        </button>
        <button
          type="button"
          class="toolbar-btn"
          :class="{ 'is-active': editor?.isActive('orderedList') }"
          @click="editor?.chain().focus().toggleOrderedList().run()"
          title="有序列表"
        >
          <OrderedListOutlined />
        </button>
      </div>

      <!-- 分隔符 -->
      <div class="toolbar-divider"></div>

      <!-- 链接和图片 -->
      <div class="toolbar-group">
        <button type="button" class="toolbar-btn" @click="setLink" title="插入链接">
          <LinkOutlined />
        </button>
        <a-upload
          :show-upload-list="false"
          accept="image/*"
          :action="uploadUrl"
          :headers="headers"
          :data="{ biz: 'temp' }"
          @change="handleImageUpload"
          class="toolbar-upload"
        >
          <button type="button" class="toolbar-btn" title="插入图片">
            <PictureOutlined />
          </button>
        </a-upload>
      </div>

      <!-- 分隔符 -->
      <div class="toolbar-divider"></div>

      <!-- 撤销重做 -->
      <div class="toolbar-group">
        <button type="button" class="toolbar-btn" @click="editor?.chain().focus().undo().run()" :disabled="!editor?.can().undo()" title="撤销">
          <UndoOutlined />
        </button>
        <button type="button" class="toolbar-btn" @click="editor?.chain().focus().redo().run()" :disabled="!editor?.can().redo()" title="重做">
          <RedoOutlined />
        </button>
      </div>
    </div>

    <!-- 编辑器内容区域 -->
    <div class="tiptap-content" :style="{ height: height }">
      <editor-content :editor="editor" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
  import { Editor, EditorContent } from '@tiptap/vue-3';
  import StarterKit from '@tiptap/starter-kit';
  import Image from '@tiptap/extension-image';
  import Link from '@tiptap/extension-link';
  import TextAlign from '@tiptap/extension-text-align';
  import Underline from '@tiptap/extension-underline';
  import { TextStyle } from '@tiptap/extension-text-style';
  import { Color } from '@tiptap/extension-color';
  import { FontFamily } from '@tiptap/extension-font-family';
  import { Highlight } from '@tiptap/extension-highlight';
  import FontSize from './extensions/FontSize';
  import { uploadUrl } from '/@/api/common/api';
  import { getToken } from '/@/utils/auth';
  import { useMessage } from '/@/hooks/web/useMessage';
  import {
    BoldOutlined,
    ItalicOutlined,
    UnderlineOutlined,
    StrikethroughOutlined,
    AlignLeftOutlined,
    AlignCenterOutlined,
    AlignRightOutlined,
    MenuOutlined,
    UnorderedListOutlined,
    OrderedListOutlined,
    LinkOutlined,
    PictureOutlined,
    UndoOutlined,
    RedoOutlined,
    DownOutlined,
    FontColorsOutlined,
    BgColorsOutlined,
  } from '@ant-design/icons-vue';

  interface Props {
    modelValue?: string;
    placeholder?: string;
    height?: string;
    disabled?: boolean;
    hideToolbar?: boolean;
    autoFocus?: boolean;
  }

  interface Emits {
    (e: 'update:modelValue', value: string): void;
    (e: 'change', value: string): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: '',
    placeholder: '请输入内容...',
    height: '300px',
    disabled: false,
    hideToolbar: false,
    autoFocus: true,
  });

  const emit = defineEmits<Emits>();

  const editor = ref<Editor>();
  const { createMessage } = useMessage();

  // 上传配置
  const headers = {
    'X-Access-Token': getToken(),
    loginSource: '0',
  };

  // 文字颜色选项
  const textColors = [
    '#000000',
    '#333333',
    '#666666',
    '#999999',
    '#cccccc',
    '#ffffff',
    '#ff0000',
    '#ff6600',
    '#ffcc00',
    '#00ff00',
    '#0066ff',
    '#6600ff',
    '#ff3366',
    '#ff9900',
    '#ffff00',
    '#66ff66',
    '#3399ff',
    '#9966ff',
    '#cc0000',
    '#cc6600',
    '#cccc00',
    '#00cc00',
    '#0066cc',
    '#6600cc',
  ];

  // 背景颜色选项
  const highlightColors = [
    '#ffff00',
    '#ffcc99',
    '#ffcccc',
    '#ccffcc',
    '#ccccff',
    '#ccffff',
    '#ffffcc',
    '#ffccff',
    '#cccccc',
    '#ff9999',
    '#99ccff',
    '#99ffcc',
    '#ffff99',
    '#ff99cc',
    '#ccff99',
    '#99cccc',
    '#cc99ff',
    '#ffcc66',
  ];

  // 初始化编辑器
  onMounted(() => {
    editor.value = new Editor({
      content: props.modelValue,
      extensions: [
        StarterKit,
        Image.configure({
          inline: true,
          allowBase64: true,
        }),
        Link.configure({
          openOnClick: false,
          HTMLAttributes: {
            target: '_blank',
            rel: 'noopener noreferrer',
          },
        }),
        TextAlign.configure({
          types: ['heading', 'paragraph'],
        }),
        Underline,
        TextStyle,
        Color.configure({
          types: ['textStyle'],
        }),
        FontFamily.configure({
          types: ['textStyle'],
        }),
        FontSize.configure({
          types: ['textStyle'],
        }),
        Highlight.configure({
          multicolor: true,
        }),
      ],
      editable: !props.disabled,
      autofocus: props.autoFocus,
      onUpdate: ({ editor }) => {
        const html = editor.getHTML();
        emit('update:modelValue', html);
        emit('change', html);
      },
    });
  });

  // 销毁编辑器
  onBeforeUnmount(() => {
    editor.value?.destroy();
  });

  // 监听内容变化
  watch(
    () => props.modelValue,
    (newValue) => {
      if (editor.value && editor.value.getHTML() !== newValue) {
        editor.value.commands.setContent(newValue);
      }
    }
  );

  // 监听禁用状态
  watch(
    () => props.disabled,
    (disabled) => {
      editor.value?.setEditable(!disabled);
    }
  );

  // 获取当前标题文本
  const getHeadingText = () => {
    if (!editor.value) return '正文';

    for (let i = 1; i <= 6; i++) {
      if (editor.value.isActive('heading', { level: i })) {
        return `标题 ${i}`;
      }
    }
    return '正文';
  };

  // 处理标题点击
  const handleHeadingClick = ({ key }: { key: string }) => {
    if (key === 'paragraph') {
      editor.value?.chain().focus().setParagraph().run();
    } else {
      const level = parseInt(key) as 1 | 2 | 3 | 4 | 5 | 6;
      editor.value?.chain().focus().toggleHeading({ level }).run();
    }
  };

  // 设置链接
  const setLink = () => {
    const previousUrl = editor.value?.getAttributes('link').href;
    const url = window.prompt('请输入链接地址:', previousUrl);

    if (url === null) {
      return;
    }

    if (url === '') {
      editor.value?.chain().focus().extendMarkRange('link').unsetLink().run();
      return;
    }

    editor.value?.chain().focus().extendMarkRange('link').setLink({ href: url }).run();
  };

  // 处理图片上传
  const handleImageUpload = ({ file }) => {
    if (file.status === 'done') {
      if (file.response && file.response.success) {
        const imageUrl = file.response.message;
        // 插入图片到编辑器
        editor.value?.chain().focus().setImage({ src: imageUrl }).run();
        createMessage.success('图片上传成功');
      } else {
        createMessage.error(file.response?.message || '图片上传失败');
      }
    } else if (file.status === 'error') {
      createMessage.error('图片上传失败');
    }
  };

  // 获取当前字体
  const getCurrentFontFamily = () => {
    if (!editor.value) return '默认字体';

    const fontFamily = editor.value.getAttributes('textStyle').fontFamily;
    if (!fontFamily) return '默认字体';

    const fontMap: Record<string, string> = {
      // 系统字体
      SimSun: '宋体',
      SimHei: '黑体',
      'Microsoft YaHei': '微软雅黑',
      KaiTi: '楷体',
      FangSong: '仿宋',
      // 苹方字体
      'PingFang Regular': '苹方常规',
      'PingFang Medium': '苹方中等',
      'PingFang Bold': '苹方加粗',
      'PingFang Heavy': '苹方特粗',
      // DIN字体
      'DIN Regular': 'DIN常规',
      'DIN Bold': 'DIN加粗',
      'DIN BlackItalic': 'DIN黑斜体',
      // 特殊字体
      'FZZongYi-M05S': '方正综艺',
      YouSheBiaoTiHei: '优设标题黑',
      // 英文字体
      Arial: 'Arial',
      'Times New Roman': 'Times New Roman',
      'Courier New': 'Courier New',
    };

    return fontMap[fontFamily] || fontFamily;
  };

  // 设置字体
  const handleFontFamilyClick = ({ key }: { key: string }) => {
    if (key === 'default') {
      editor.value?.chain().focus().unsetFontFamily().run();
    } else {
      editor.value?.chain().focus().setFontFamily(key).run();
    }
  };

  // 获取当前字号
  const getCurrentFontSize = () => {
    if (!editor.value) return '16px';

    const fontSize = editor.value.getAttributes('textStyle').fontSize;
    return fontSize || '16px';
  };

  // 设置字号
  const handleFontSizeClick = ({ key }: { key: string }) => {
    editor.value?.chain().focus().setFontSize(key).run();
  };

  // 获取当前文字颜色
  const getCurrentTextColor = () => {
    if (!editor.value) return '#000000';

    const color = editor.value.getAttributes('textStyle').color;
    return color || '#000000';
  };

  // 设置文字颜色
  const setTextColor = (color: string) => {
    editor.value?.chain().focus().setColor(color).run();
  };

  // 清除文字颜色
  const clearTextColor = () => {
    editor.value?.chain().focus().unsetColor().run();
  };

  // 获取当前背景颜色
  const getCurrentHighlightColor = () => {
    if (!editor.value) return 'transparent';

    const highlight = editor.value.getAttributes('highlight').color;
    return highlight || 'transparent';
  };

  // 设置背景颜色
  const setHighlightColor = (color: string) => {
    editor.value?.chain().focus().setHighlight({ color }).run();
  };

  // 清除背景颜色
  const clearHighlightColor = () => {
    editor.value?.chain().focus().unsetHighlight().run();
  };
</script>

<style lang="less" scoped>
  .tiptap-editor {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    background: #fff;

    &--disabled {
      background: #f5f5f5;

      .tiptap-toolbar {
        opacity: 0.6;
        pointer-events: none;
      }
    }
  }

  .tiptap-toolbar {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;
    border-radius: 6px 6px 0 0;
    flex-wrap: wrap;
    gap: 4px;
  }

  .toolbar-group {
    display: flex;
    align-items: center;
    gap: 2px;
  }

  .toolbar-divider {
    width: 1px;
    height: 20px;
    background: #d9d9d9;
    margin: 0 8px;
  }

  .toolbar-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    border-radius: 4px;
    cursor: pointer;
    color: #666;
    transition: all 0.2s;
    position: relative;

    &:hover {
      background: #e6f7ff;
      color: #1890ff;
    }

    &.is-active {
      background: #1890ff;
      color: #fff;
    }

    &:disabled {
      opacity: 0.4;
      cursor: not-allowed;
    }

    &--dropdown {
      width: auto;
      padding: 0 8px;
      gap: 4px;

      span {
        font-size: 14px;
      }
    }

    .color-indicator {
      position: absolute;
      bottom: 2px;
      left: 50%;
      transform: translateX(-50%);
      width: 16px;
      height: 3px;
      border-radius: 1px;
      border: 1px solid #ccc;
    }
  }

  .color-picker {
    padding: 8px;
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

    .color-row {
      display: flex;
      gap: 4px;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .color-item {
      width: 20px;
      height: 20px;
      border-radius: 3px;
      border: 1px solid #ddd;
      cursor: pointer;
      transition: transform 0.2s;

      &:hover {
        transform: scale(1.1);
        border-color: #1890ff;
      }
    }

    .color-clear-btn {
      padding: 4px 8px;
      border: 1px solid #d9d9d9;
      background: #fff;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      color: #666;

      &:hover {
        border-color: #1890ff;
        color: #1890ff;
      }
    }
  }

  .tiptap-content {
    overflow-y: auto;

    :deep(.ProseMirror) {
      padding: 12px;
      outline: none;
      min-height: 200px;

      &:focus {
        outline: none;
      }

      p {
        margin: 0 0 8px 0;

        &:last-child {
          margin-bottom: 0;
        }
      }

      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        margin: 16px 0 8px 0;
        font-weight: 600;

        &:first-child {
          margin-top: 0;
        }
      }

      h1 {
        font-size: 2em;
      }
      h2 {
        font-size: 1.5em;
      }
      h3 {
        font-size: 1.25em;
      }
      h4 {
        font-size: 1.1em;
      }
      h5 {
        font-size: 1em;
      }
      h6 {
        font-size: 0.9em;
      }

      ul,
      ol {
        margin: 8px 0;
        padding-left: 24px;

        li {
          margin: 4px 0;
        }
      }

      a {
        color: #1890ff;
        text-decoration: underline;

        &:hover {
          color: #40a9ff;
        }
      }

      img {
        max-width: 100%;
        height: auto;
        border-radius: 4px;
      }

      strong {
        font-weight: 600;
      }

      em {
        font-style: italic;
      }

      u {
        text-decoration: underline;
      }

      s {
        text-decoration: line-through;
      }
    }
  }

  .toolbar-upload {
    display: inline-block;

    :deep(.ant-upload) {
      display: inline-block;
    }
  }
</style>
