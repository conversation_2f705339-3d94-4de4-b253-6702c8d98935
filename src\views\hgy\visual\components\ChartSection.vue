<template>
  <div class="chart-section">
    <!-- 上半部分：溢价趋势图 -->
    <div class="chart-container trend-chart">
      <div class="chart-bg"></div>
      <div class="chart-content">
        <div class="chart-title">标的溢价趋势</div>
        <div class="chart-wrapper">
          <div ref="trendChartRef" class="chart"></div>
        </div>
      </div>
    </div>

    <!-- 下半部分：成交额排名柱状图 -->
    <div class="chart-container bar-chart">
      <div class="chart-bg"></div>
      <div class="chart-content">
        <div class="chart-title">成交额排名</div>
        <div class="chart-wrapper">
          <div ref="barChartRef" class="chart"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted, nextTick } from 'vue';
  import * as echarts from 'echarts';

  const trendChartRef = ref<HTMLElement>();
  const barChartRef = ref<HTMLElement>();
  let trendChart: echarts.ECharts | null = null;
  let barChart: echarts.ECharts | null = null;

  // 初始化溢价趋势图
  const initTrendChart = () => {
    if (!trendChartRef.value) return;

    trendChart = echarts.init(trendChartRef.value);

    const option = {
      backgroundColor: 'transparent',
      grid: {
        top: '15%',
        left: '8%',
        right: '8%',
        bottom: '15%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月'],
        axisLine: {
          lineStyle: {
            color: '#12e6db',
            width: 1,
          },
        },
        axisLabel: {
          color: '#ffffff',
          fontSize: 12,
        },
        axisTick: {
          show: false,
        },
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: false,
        },
        axisLabel: {
          color: '#ffffff',
          fontSize: 12,
          formatter: '{value}%',
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)',
            type: 'dashed',
          },
        },
      },
      series: [
        {
          name: '溢价率',
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            width: 3,
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: '#12e6db' },
              { offset: 1, color: '#097884' },
            ]),
          },
          itemStyle: {
            color: '#12e6db',
            borderColor: '#ffffff',
            borderWidth: 2,
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(18, 230, 219, 0.3)' },
              { offset: 1, color: 'rgba(18, 230, 219, 0.05)' },
            ]),
          },
          data: [85, 92, 78, 95, 88, 96, 89, 93, 87, 91],
          animationDuration: 2000,
          animationEasing: 'cubicOut',
        },
      ],
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#12e6db',
        borderWidth: 1,
        textStyle: {
          color: '#ffffff',
        },
        formatter: '{b}: {c}%',
      },
    };

    trendChart.setOption(option);
  };

  // 初始化柱状图
  const initBarChart = () => {
    if (!barChartRef.value) return;

    barChart = echarts.init(barChartRef.value);

    const option = {
      backgroundColor: 'transparent',
      grid: {
        top: '15%',
        left: '8%',
        right: '8%',
        bottom: '15%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: ['项目A', '项目B', '项目C', '项目D', '项目E', '项目F', '项目G', '项目H', '项目I', '项目J'],
        axisLine: {
          lineStyle: {
            color: '#12e6db',
            width: 1,
          },
        },
        axisLabel: {
          color: '#ffffff',
          fontSize: 12,
          rotate: 45,
        },
        axisTick: {
          show: false,
        },
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: false,
        },
        axisLabel: {
          color: '#ffffff',
          fontSize: 12,
          formatter: '{value}万',
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)',
            type: 'dashed',
          },
        },
      },
      series: [
        {
          name: '成交额',
          type: 'bar',
          barWidth: '60%',
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#2BCCFF' },
              { offset: 1, color: '#1A8FCC' },
            ]),
            borderRadius: [4, 4, 0, 0],
          },
          data: [5969, 4569, 3969, 2969, 1969, 969, 869, 769, 669, 569],
          animationDuration: 2000,
          animationEasing: 'cubicOut',
          animationDelay: (idx: number) => idx * 100,
        },
      ],
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#2BCCFF',
        borderWidth: 1,
        textStyle: {
          color: '#ffffff',
        },
        formatter: '{b}: {c}万元',
      },
    };

    barChart.setOption(option);
  };

  // 窗口大小变化时重新调整图表
  const handleResize = () => {
    trendChart?.resize();
    barChart?.resize();
  };

  onMounted(async () => {
    await nextTick();
    initTrendChart();
    initBarChart();
    window.addEventListener('resize', handleResize);
  });

  onUnmounted(() => {
    trendChart?.dispose();
    barChart?.dispose();
    window.removeEventListener('resize', handleResize);
  });
</script>

<style lang="less" scoped>
  @import '../styles/fullScreen/ChartSection.less';
</style>
