/**
 * 响应式布局 Composable
 * 提供现代化的响应式布局管理功能
 */
import { ref, reactive, onMounted, onUnmounted } from 'vue';

// 断点定义
export const BREAKPOINTS = {
  xs: 0,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
  '2xl': 1600,
} as const;

export type Breakpoint = keyof typeof BREAKPOINTS;

// 屏幕尺寸信息
export interface ScreenSize {
  width: number;
  height: number;
  aspectRatio: number;
}

// 设备类型
export type DeviceType = 'mobile' | 'tablet' | 'desktop' | 'large-desktop';

// 布局配置
export interface LayoutConfig {
  columns: number;
  gap: string;
  padding: string;
  fontSize: {
    base: string;
    title: string;
    small: string;
  };
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
}

/**
 * 响应式布局 Hook
 */
export function useResponsiveLayout() {
  // 当前屏幕尺寸
  const screenSize = reactive<ScreenSize>({
    width: window.innerWidth,
    height: window.innerHeight,
    aspectRatio: window.innerWidth / window.innerHeight,
  });

  // 当前断点
  const breakpoint = ref<Breakpoint>('lg');
  
  // 设备类型
  const deviceType = ref<DeviceType>('desktop');

  // 是否为移动端
  const isMobile = ref(false);
  
  // 是否为平板
  const isTablet = ref(false);
  
  // 是否为桌面端
  const isDesktop = ref(true);

  // 布局配置
  const layoutConfig = reactive<LayoutConfig>({
    columns: 12,
    gap: '16px',
    padding: '16px',
    fontSize: {
      base: '16px',
      title: '24px',
      small: '14px',
    },
    spacing: {
      xs: '8px',
      sm: '12px',
      md: '16px',
      lg: '24px',
      xl: '32px',
    },
  });

  /**
   * 获取当前断点
   */
  const getCurrentBreakpoint = (width: number): Breakpoint => {
    if (width >= BREAKPOINTS['2xl']) return '2xl';
    if (width >= BREAKPOINTS.xl) return 'xl';
    if (width >= BREAKPOINTS.lg) return 'lg';
    if (width >= BREAKPOINTS.md) return 'md';
    if (width >= BREAKPOINTS.sm) return 'sm';
    return 'xs';
  };

  /**
   * 获取设备类型
   */
  const getDeviceType = (width: number): DeviceType => {
    if (width >= 1600) return 'large-desktop';
    if (width >= 1024) return 'desktop';
    if (width >= 768) return 'tablet';
    return 'mobile';
  };

  /**
   * 更新布局配置
   */
  const updateLayoutConfig = (currentBreakpoint: Breakpoint) => {
    switch (currentBreakpoint) {
      case 'xs':
        Object.assign(layoutConfig, {
          columns: 1,
          gap: '8px',
          padding: '8px',
          fontSize: {
            base: '14px',
            title: '18px',
            small: '12px',
          },
          spacing: {
            xs: '4px',
            sm: '6px',
            md: '8px',
            lg: '12px',
            xl: '16px',
          },
        });
        break;
      case 'sm':
        Object.assign(layoutConfig, {
          columns: 2,
          gap: '12px',
          padding: '12px',
          fontSize: {
            base: '15px',
            title: '20px',
            small: '13px',
          },
          spacing: {
            xs: '6px',
            sm: '8px',
            md: '12px',
            lg: '16px',
            xl: '20px',
          },
        });
        break;
      case 'md':
        Object.assign(layoutConfig, {
          columns: 3,
          gap: '16px',
          padding: '16px',
          fontSize: {
            base: '16px',
            title: '22px',
            small: '14px',
          },
          spacing: {
            xs: '8px',
            sm: '10px',
            md: '14px',
            lg: '18px',
            xl: '24px',
          },
        });
        break;
      case 'lg':
        Object.assign(layoutConfig, {
          columns: 4,
          gap: '20px',
          padding: '20px',
          fontSize: {
            base: '16px',
            title: '24px',
            small: '14px',
          },
          spacing: {
            xs: '8px',
            sm: '12px',
            md: '16px',
            lg: '24px',
            xl: '32px',
          },
        });
        break;
      case 'xl':
        Object.assign(layoutConfig, {
          columns: 6,
          gap: '24px',
          padding: '24px',
          fontSize: {
            base: '17px',
            title: '26px',
            small: '15px',
          },
          spacing: {
            xs: '10px',
            sm: '14px',
            md: '18px',
            lg: '28px',
            xl: '36px',
          },
        });
        break;
      case '2xl':
        Object.assign(layoutConfig, {
          columns: 8,
          gap: '32px',
          padding: '32px',
          fontSize: {
            base: '18px',
            title: '28px',
            small: '16px',
          },
          spacing: {
            xs: '12px',
            sm: '16px',
            md: '20px',
            lg: '32px',
            xl: '40px',
          },
        });
        break;
    }
  };

  /**
   * 更新屏幕信息
   */
  const updateScreenInfo = () => {
    const width = window.innerWidth;
    const height = window.innerHeight;
    
    // 更新屏幕尺寸
    screenSize.width = width;
    screenSize.height = height;
    screenSize.aspectRatio = width / height;
    
    // 更新断点
    const currentBreakpoint = getCurrentBreakpoint(width);
    breakpoint.value = currentBreakpoint;
    
    // 更新设备类型
    const currentDeviceType = getDeviceType(width);
    deviceType.value = currentDeviceType;
    
    // 更新设备标识
    isMobile.value = currentDeviceType === 'mobile';
    isTablet.value = currentDeviceType === 'tablet';
    isDesktop.value = currentDeviceType === 'desktop' || currentDeviceType === 'large-desktop';
    
    // 更新布局配置
    updateLayoutConfig(currentBreakpoint);
  };

  /**
   * 防抖函数
   */
  const debounce = <T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): ((...args: Parameters<T>) => void) => {
    let timeout: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(null, args), wait);
    };
  };

  // 防抖的更新函数
  const debouncedUpdateLayout = debounce(updateScreenInfo, 150);

  /**
   * 更新布局（公开方法）
   */
  const updateLayout = () => {
    debouncedUpdateLayout();
  };

  /**
   * 检查是否匹配指定断点
   */
  const matchBreakpoint = (bp: Breakpoint): boolean => {
    const currentWidth = screenSize.width;
    const targetWidth = BREAKPOINTS[bp];
    
    if (bp === '2xl') {
      return currentWidth >= targetWidth;
    }
    
    const nextBreakpointKey = Object.keys(BREAKPOINTS)[
      Object.keys(BREAKPOINTS).indexOf(bp) + 1
    ] as Breakpoint;
    
    if (!nextBreakpointKey) {
      return currentWidth >= targetWidth;
    }
    
    const nextBreakpointWidth = BREAKPOINTS[nextBreakpointKey];
    return currentWidth >= targetWidth && currentWidth < nextBreakpointWidth;
  };

  /**
   * 检查是否大于等于指定断点
   */
  const isBreakpointUp = (bp: Breakpoint): boolean => {
    return screenSize.width >= BREAKPOINTS[bp];
  };

  /**
   * 检查是否小于指定断点
   */
  const isBreakpointDown = (bp: Breakpoint): boolean => {
    return screenSize.width < BREAKPOINTS[bp];
  };

  /**
   * 获取响应式值
   */
  const getResponsiveValue = <T>(values: Partial<Record<Breakpoint, T>>): T | undefined => {
    const sortedBreakpoints = Object.keys(BREAKPOINTS).reverse() as Breakpoint[];
    
    for (const bp of sortedBreakpoints) {
      if (values[bp] !== undefined && isBreakpointUp(bp)) {
        return values[bp];
      }
    }
    
    return values.xs;
  };

  // 组件挂载时初始化
  onMounted(() => {
    updateScreenInfo();
    window.addEventListener('resize', debouncedUpdateLayout);
  });

  // 组件卸载时清理
  onUnmounted(() => {
    window.removeEventListener('resize', debouncedUpdateLayout);
  });

  return {
    // 响应式数据
    screenSize,
    breakpoint,
    deviceType,
    isMobile,
    isTablet,
    isDesktop,
    layoutConfig,
    
    // 方法
    updateLayout,
    matchBreakpoint,
    isBreakpointUp,
    isBreakpointDown,
    getResponsiveValue,
    getCurrentBreakpoint,
    getDeviceType,
    
    // 常量
    BREAKPOINTS,
  };
}
