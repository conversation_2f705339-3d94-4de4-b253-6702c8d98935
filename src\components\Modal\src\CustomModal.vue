<template>
  <a-modal
    v-model:open="visible"
    :width="width"
    :centered="centered"
    :closable="false"
    :footer="null"
    :mask-closable="maskClosable"
    :destroy-on-close="destroyOnClose"
    :get-container="getContainer"
    wrap-class-name="custom-modal-wrap"
    class="custom-modal"
    @cancel="handleCancel"
  >
    <!-- 自定义头部 -->
    <template #title>
      <div class="custom-modal-header">
        <div class="header-left">
          <Icon icon="modal-logo|svg" class="logo-icon" />
        </div>
        <div class="header-center">
          <span class="modal-title">{{ title }}</span>
        </div>
        <div class="header-right">
          <SvgIcon name="close" class="close-icon" @click="handleClose" />
        </div>
      </div>
    </template>

    <!-- 内容区域 -->
    <div class="custom-modal-content">
      <slot></slot>
    </div>

    <!-- 底部区域 -->
    <div v-if="showFooter" class="custom-modal-footer">
      <slot name="footer">
        <div class="default-footer">
          <a-button v-if="showCancelButton" @click="handleCancel" class="cancel-btn">
            {{ cancelText }}
          </a-button>
          <a-button v-if="showConfirmButton" type="primary" @click="handleConfirm" class="confirm-btn">
            {{ confirmText }}
          </a-button>
        </div>
      </slot>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, watch, computed } from 'vue';
  import { Icon } from '/@/components/Icon';
  import SvgIcon from '../../Icon/src/SvgIcon.vue';

  interface Props {
    open?: boolean;
    title?: string;
    width?: string | number;
    centered?: boolean;
    maskClosable?: boolean;
    destroyOnClose?: boolean;
    getContainer?: string | HTMLElement | (() => HTMLElement) | false;
    showFooter?: boolean;
    showCancelButton?: boolean;
    showConfirmButton?: boolean;
    cancelText?: string;
    confirmText?: string;
  }

  interface Emits {
    (e: 'update:open', value: boolean): void;
    (e: 'cancel'): void;
    (e: 'confirm'): void;
    (e: 'close'): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    open: false,
    title: '弹窗标题',
    width: 520,
    centered: true,
    maskClosable: true,
    destroyOnClose: false,
    getContainer: false,
    showFooter: true,
    showCancelButton: true,
    showConfirmButton: true,
    cancelText: '取消',
    confirmText: '确认',
  });

  const emit = defineEmits<Emits>();

  const visible = ref(props.open);

  // 监听外部传入的open状态
  watch(
    () => props.open,
    (newValue) => {
      visible.value = newValue;
    }
  );

  // 监听内部visible状态变化
  watch(visible, (newValue) => {
    emit('update:open', newValue);
  });

  const handleCancel = () => {
    visible.value = false;
    emit('cancel');
  };

  const handleConfirm = () => {
    emit('confirm');
  };

  const handleClose = () => {
    visible.value = false;
    emit('close');
  };
</script>

<style lang="less" scoped>
  :deep(.custom-modal-wrap) {
    .ant-modal {
      .ant-modal-content {
        padding: 0;
        overflow: hidden;
        border-radius: 8px;
      }

      .ant-modal-header {
        padding: 0;
        margin: 0;
        border-bottom: none;
        background: transparent;
      }

      .ant-modal-body {
        padding: 0;
      }
    }
  }

  .custom-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 48px;
    padding: 0 16px;
    background: linear-gradient(180deg, rgba(0, 76, 102, 0.9) 0%, rgba(0, 76, 102, 0.6) 100%);
    position: relative;

    .header-left {
      display: flex;
      align-items: center;
      flex-shrink: 0;

      .logo-icon {
        width: 20px;
        height: 21px;
        color: #fff;
      }
    }

    .header-center {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);

      .modal-title {
        font-family: 'PingFang Bold';
        font-size: 18px;
        color: #fff;
        white-space: nowrap;
      }
    }

    .header-right {
      display: flex;
      align-items: center;
      flex-shrink: 0;

      .close-icon {
        width: 16px;
        height: 16px;
        color: #fff;
        cursor: pointer;
        transition: all 0.2s;

        &:hover {
          color: #ef0004;
          transform: scale(1.1);
        }
      }
    }
  }

  .custom-modal-content {
    padding: 0 49px;
    min-height: 100px;
    background: #fff;
  }

  .custom-modal-footer {
    margin-bottom: 20px;
    background: #fff;

    .default-footer {
      display: flex;
      justify-content: center;
      gap: 12px;

      .cancel-btn {
        min-width: 80px;
        height: 36px;
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        background: #fff;
        color: #666;
        font-size: 14px;

        &:hover {
          border-color: #004c66;
          color: #004c66;
        }
      }

      .confirm-btn {
        min-width: 80px;
        height: 36px;
        border-radius: 6px;
        background: rgba(0, 76, 102, 0.9);
        border-color: rgba(0, 76, 102, 0.9);
        color: #fff;
        font-size: 14px;

        &:hover {
          background: rgba(0, 76, 102, 1);
          border-color: rgba(0, 76, 102, 1);
        }
      }
    }
  }

  :deep(.ant-btn-primary) {
    margin-right: 0;
    margin-left: 8px;
    box-shadow: 0 0 0 rgba(3, 38, 43, 0.42);
  }
</style>
