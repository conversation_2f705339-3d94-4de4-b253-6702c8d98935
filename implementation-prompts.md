# 板块实现提示词

## 🎯 提示词使用说明

每个提示词都是独立的，可以直接复制到AI助手中使用。建议按照以下顺序实现：

1. 发布供求信息板块
2. 我的发布板块
3. 我的账户板块
4. 消息中心板块

---

## 📝 提示词1：发布供求信息板块

```
请帮我在Vue 3 + TypeScript + Ant Design Vue项目中实现一个发布供求信息板块。

### 功能需求：
1. **双模式发布**：支持发布供应信息和需求信息
2. **多步骤表单**：采用步骤条形式，分为3个步骤
3. **物资管理**：支持物资类型选择、详细描述、图片上传
4. **地理位置**：支持省市区选择和详细地址填写
5. **联系方式**：联系人和电话信息
6. **预览发布**：最后一步预览所有信息并发布

### 技术要求：
- 使用Vue 3 Composition API + TypeScript
- 使用Ant Design Vue组件库
- 表单验证使用async-validator
- 支持文件上传（图片和附件）
- 响应式设计，支持PC和移动端

### 页面结构：
```

src/views/supplyAndDemand/ ├── supply/ │ ├── index.vue # 供应信息发布主页 │ └── components/ │ ├── Step1.vue # 基本信息 │ ├── Step2.vue # 物资详情 │ └── Step3.vue # 预览发布 ├── demand/ │ ├── index.vue # 需求信息发布主页 │ └── components/ │ ├── Step1.vue # 基本信息 │ ├── Step2.vue # 物资详情 │ └── Step3.vue # 预览发布 └── components/ ├── MaterialTypeSelect.vue # 物资类型选择 ├── AddressSelect.vue # 地址选择 └── FileUpload.vue # 文件上传

````

### API接口：
```typescript
// 保存供求信息
POST /hgy/supplyDemand/hgySupplyDemand/saveOrderAndSupplyDemand
// 更新供求信息
PUT /hgy/supplyDemand/hgySupplyDemand/updateSupplyDemand
// 获取物资类型树
GET /hgy/material/hgyMaterialType/getMaterialTree
// 文件上传
POST /sys/common/upload
````

### 数据结构：

```typescript
interface SupplyDemandForm {
  type: '4' | '5'; // 4-供应, 5-需求
  infoTitle: string;
  highlights?: string;
  materialType: string;
  materialDesc: string;
  quantity: number;
  unit: string;
  price: number;
  brand?: string;
  model?: string;
  depreciationDegree: number;
  province: string;
  city: string;
  district: string;
  address: string;
  relationUser: string;
  relationPhone: string;
  validType: string;
  validDate?: string;
  servicePayType: '1' | '2';
  attachments?: string[];
}
```

请实现完整的功能，包括表单验证、数据提交、错误处理等。

```

---

## 📝 提示词2：我的发布板块

```

请帮我在Vue 3 + TypeScript + Ant Design Vue项目中实现一个我的发布管理板块。

### 功能需求：

1. **发布列表**：分页显示用户的所有发布信息
2. **分类筛选**：按供应/需求类型、状态筛选
3. **搜索功能**：支持标题、物资类型关键词搜索
4. **状态管理**：草稿、已发布、已过期、已下架等状态
5. **批量操作**：批量删除、批量下架
6. **快速操作**：编辑、删除、复制、查看详情
7. **数据统计**：显示各状态数量统计

### 技术要求：

- 使用Vue 3 Composition API + TypeScript
- 使用Ant Design Vue的Table、Modal、Drawer等组件
- 支持表格和卡片两种展示模式
- 实现虚拟滚动优化大数据量性能
- 响应式设计

### 页面结构：

```
src/views/publishList/
├── index.vue               # 发布列表主页
├── supply/
│   └── index.vue          # 我的供应列表
├── demand/
│   └── index.vue          # 我的需求列表
└── components/
    ├── PublishTable.vue    # 发布列表表格
    ├── PublishCard.vue     # 发布信息卡片
    ├── FilterPanel.vue     # 筛选面板
    ├── BatchActions.vue    # 批量操作
    └── PublishDetail.vue   # 发布详情抽屉
```

### API接口：

```typescript
// 获取发布列表
GET / hgy / supplyDemand / hgySupplyDemand / list;
// 删除发布
DELETE /
  hgy /
  supplyDemand /
  hgySupplyDemand /
  delete (
    // 批量删除
    POST
  ) /
  hgy /
  supplyDemand /
  hgySupplyDemand /
  deleteBatch;
// 更新状态
PUT / hgy / supplyDemand / hgySupplyDemand / updateStatus;
// 获取统计数据
GET / hgy / supplyDemand / hgySupplyDemand / statistics;
```

### 状态定义：

```typescript
enum PublishStatus {
  DRAFT = 1, // 草稿
  PUBLISHED = 2, // 已发布
  EXPIRED = 3, // 已过期
  OFFLINE = 4, // 已下架
  DELETED = 5, // 已删除
}

interface PublishItem {
  id: string;
  type: '4' | '5';
  infoTitle: string;
  materialType: string;
  price: number;
  quantity: number;
  unit: string;
  status: PublishStatus;
  viewCount: number;
  createTime: string;
  updateTime: string;
  validDate?: string;
}
```

### 特殊要求：

- 支持拖拽排序
- 支持导出Excel功能
- 实现无限滚动加载
- 添加操作确认对话框
- 支持快捷键操作

请实现完整的功能，包括列表展示、筛选搜索、批量操作、状态管理等。

```

---

## 📝 提示词3：我的账户板块

```

请帮我在Vue 3 + TypeScript + Ant Design Vue项目中实现一个我的账户管理板块。

### 功能需求：

1. **账户概览**：显示用户基本信息、资产概览、统计数据
2. **积分管理**：积分余额、明细记录、等级体系、积分规则
3. **库存管理**：物资库存列表、分类管理、库存操作、预警提醒

### 技术要求：

- 使用Vue 3 Composition API + TypeScript
- 使用Ant Design Vue + ECharts图表
- 支持数据可视化展示
- 实现实时数据更新
- 响应式设计

### 页面结构：

```
src/views/account/
├── index.vue               # 账户主页（概览）
├── overview/
│   └── index.vue          # 账户概览
├── integral/
│   ├── index.vue          # 积分管理主页
│   └── components/
│       ├── IntegralChart.vue    # 积分趋势图
│       ├── IntegralRecord.vue   # 积分明细
│       ├── IntegralRules.vue    # 积分规则
│       └── LevelSystem.vue      # 等级体系
└── inventory/
    ├── index.vue          # 库存管理主页
    └── components/
        ├── InventoryList.vue    # 库存列表
        ├── InventoryChart.vue   # 库存统计图
        ├── StockAlert.vue       # 库存预警
        └── InventoryForm.vue    # 库存操作表单
```

### API接口：

```typescript
// 账户概览
GET / hgy / account / overview;
// 积分相关
GET / hgy / account / integral / balance;
GET / hgy / account / integral / records;
GET / hgy / account / integral / rules;
// 库存相关
GET / hgy / account / inventory / list;
POST / hgy / account / inventory / add;
PUT / hgy / account / inventory / update;
DELETE / hgy / account / inventory / delete GET / hgy / account / inventory / statistics;
```

### 数据结构：

```typescript
interface AccountOverview {
  userId: string;
  username: string;
  avatar?: string;
  level: number;
  balance: number;
  frozenAmount: number;
  integral: number;
  integralLevel: number;
  publishCount: number;
  tradeCount: number;
  creditScore: number;
}

interface IntegralRecord {
  id: string;
  type: 'EARN' | 'SPEND';
  amount: number;
  description: string;
  createTime: string;
  balance: number;
}

interface InventoryItem {
  id: string;
  name: string;
  category: string;
  quantity: number;
  unit: string;
  price: number;
  location: string;
  status: 'NORMAL' | 'LOW_STOCK' | 'OUT_OF_STOCK';
  lastUpdateTime: string;
}
```

### 特殊要求：

- 使用ECharts实现数据可视化
- 支持数据导出功能
- 实现库存预警功能
- 添加数据刷新机制
- 支持暗黑模式

请实现完整的功能，包括数据展示、图表可视化、表单操作等。

```

---

## 📝 提示词4：消息中心板块

```

请帮我在Vue 3 + TypeScript + Ant Design Vue项目中实现一个消息中心板块。

### 功能需求：

1. **收件箱**：接收消息列表、未读标识、消息搜索、批量操作
2. **发件箱**：已发消息列表、发送状态、消息撤回
3. **系统消息**：系统通知、业务提醒、安全消息
4. **消息详情**：查看完整消息内容和附件
5. **实时通知**：WebSocket实时消息推送

### 技术要求：

- 使用Vue 3 Composition API + TypeScript
- 使用Ant Design Vue组件库
- 集成WebSocket实现实时消息
- 支持消息分类和筛选
- 响应式设计

### 页面结构：

```
src/views/messageCenter/
├── index.vue               # 消息中心主页
├── inbox/
│   ├── index.vue          # 收件箱
│   └── components/
│       ├── MessageList.vue     # 消息列表
│       ├── MessageItem.vue     # 消息项
│       └── MessageDetail.vue   # 消息详情
├── outbox/
│   ├── index.vue          # 发件箱
│   └── components/
│       ├── SentList.vue        # 已发消息列表
│       └── ComposeMessage.vue  # 写消息
├── system/
│   ├── index.vue          # 系统消息
│   └── components/
│       ├── SystemList.vue      # 系统消息列表
│       └── NoticeDetail.vue    # 通知详情
└── components/
    ├── MessageSearch.vue       # 消息搜索
    ├── MessageFilter.vue       # 消息筛选
    └── BatchActions.vue        # 批量操作
```

### API接口：

```typescript
// 收件箱
GET / hgy / message / inbox / list;
GET / hgy / message / inbox / detail;
PUT / hgy / message / inbox / read;
DELETE /
  hgy /
  message /
  inbox /
  delete (
    // 发件箱
    GET
  ) /
  hgy /
  message /
  outbox /
  list;
POST / hgy / message / outbox / send;
DELETE /
  hgy /
  message /
  outbox /
  delete (
    // 系统消息
    GET
  ) /
  hgy /
  message /
  system /
  list;
PUT / hgy / message / system / read;
// 通用
GET / hgy / message / unread / count;
```

### 数据结构：

```typescript
enum MessageType {
  SYSTEM = 'SYSTEM',
  PRIVATE = 'PRIVATE',
  TRADE = 'TRADE',
  NOTICE = 'NOTICE',
}

interface MessageItem {
  id: string;
  type: MessageType;
  title: string;
  content: string;
  sender: string;
  senderName: string;
  receiver: string;
  isRead: boolean;
  createTime: string;
  attachments?: string[];
}

interface UnreadCount {
  inbox: number;
  system: number;
  total: number;
}
```

### 特殊要求：

- 集成WebSocket实现实时消息推送
- 支持消息草稿保存
- 实现消息搜索高亮
- 添加消息已读回执
- 支持富文本消息内容
- 实现消息分页加载

请实现完整的功能，包括消息列表、实时推送、搜索筛选、批量操作等。

```

---

## 🔧 通用配置提示词

```

请帮我配置这些板块的通用功能：

### 1. 路由配置

在 src/router/routes/modules/ 下创建对应的路由文件，包含：

- 路由路径和组件映射
- 路由元信息（标题、图标、权限等）
- 嵌套路由结构
- 路由守卫配置

### 2. API配置

在 src/api/ 下创建对应的API文件，包含：

- 统一的请求封装
- 错误处理机制
- 请求拦截器配置
- 响应数据类型定义

### 3. 状态管理

使用Pinia创建对应的store，包含：

- 状态定义和初始化
- 异步操作封装
- 数据缓存机制
- 状态持久化

### 4. 通用组件

创建可复用的通用组件，包含：

- 分页组件
- 搜索组件
- 筛选组件
- 上传组件
- 图表组件

### 5. 工具函数

创建通用的工具函数，包含：

- 日期格式化
- 数据验证
- 文件处理
- 数组操作
- 字符串处理

请提供完整的配置代码和使用示例。

```

---

## 🚀 快速开始指南

### 实施步骤建议：

#### 第一阶段：基础配置（1-2天）
1. **项目初始化**：确保灰谷网项目环境正常
2. **通用配置**：使用通用配置提示词设置路由、API、状态管理
3. **基础组件**：创建通用组件库

#### 第二阶段：核心功能（3-5天）
1. **发布供求信息**：使用提示词1实现发布功能
2. **我的发布管理**：使用提示词2实现列表管理
3. **基础测试**：测试发布和管理功能

#### 第三阶段：扩展功能（2-3天）
1. **账户管理**：使用提示词3实现账户功能
2. **消息中心**：使用提示词4实现消息功能
3. **集成测试**：测试所有功能集成

#### 第四阶段：优化完善（1-2天）
1. **性能优化**：优化加载速度和用户体验
2. **样式调整**：统一UI风格和响应式适配
3. **最终测试**：全面功能测试和bug修复

### 使用提示词的最佳实践：

1. **逐个实现**：不要同时使用多个提示词，按顺序逐个实现
2. **详细说明**：向AI说明你的具体项目环境和特殊需求
3. **分步验证**：每完成一个功能模块就进行测试验证
4. **代码审查**：检查生成的代码质量和安全性
5. **文档记录**：记录实现过程中的问题和解决方案

### 常见问题预防：

1. **依赖冲突**：确保所有依赖版本兼容
2. **API接口**：确认后端接口是否已实现
3. **权限控制**：添加必要的权限验证
4. **错误处理**：完善错误处理和用户提示
5. **性能优化**：注意大数据量的性能问题

### 技术支持：

如果在实现过程中遇到问题：
1. 检查控制台错误信息
2. 确认API接口返回数据格式
3. 验证组件依赖是否正确导入
4. 查看网络请求是否正常
5. 寻求技术团队支持
```
