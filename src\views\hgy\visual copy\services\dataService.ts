/**
 * 数据可视化大屏数据服务
 * 提供模拟数据和数据更新功能
 */

// 数据接口定义
export interface KPIData {
  totalAmount: number;        // 成交总额
  premiumAmount: number;      // 溢价总额
  premiumRate: number;        // 总溢价率
  totalTargets: number;       // 标的总量
  assetDisposalTotal: number; // 资产处置总量
  targetAmount: number;       // 标的成交额
  assetAmount: number;        // 资产处置成交额
  targetPremiumRate: number;  // 标的溢价率
}

export interface TrendData {
  month: string;
  value: number;
}

export interface RankingItem {
  name: string;
  value: number;
  rank: number;
}

export interface ChartData {
  trendData: TrendData[];
  barData: { name: string; value: number }[];
  premiumAmountRanking: RankingItem[];
  premiumRateRanking: RankingItem[];
  assetDisposalRanking: RankingItem[];
}

// 模拟数据生成器
class DataService {
  private kpiData: KPIData = {
    totalAmount: 59645956,
    premiumAmount: 5956,
    premiumRate: 95.00,
    totalTargets: 5956,
    assetDisposalTotal: 2956,
    targetAmount: 35956,
    assetAmount: 23689,
    targetPremiumRate: 87.50,
  };

  private chartData: ChartData = {
    trendData: [
      { month: '1月', value: 85 },
      { month: '2月', value: 92 },
      { month: '3月', value: 78 },
      { month: '4月', value: 95 },
      { month: '5月', value: 88 },
      { month: '6月', value: 96 },
      { month: '7月', value: 89 },
      { month: '8月', value: 93 },
      { month: '9月', value: 87 },
      { month: '10月', value: 91 },
    ],
    barData: [
      { name: '项目A', value: 5969 },
      { name: '项目B', value: 4569 },
      { name: '项目C', value: 3969 },
      { name: '项目D', value: 2969 },
      { name: '项目E', value: 1969 },
      { name: '项目F', value: 969 },
      { name: '项目G', value: 869 },
      { name: '项目H', value: 769 },
      { name: '项目I', value: 669 },
      { name: '项目J', value: 569 },
    ],
    premiumAmountRanking: [
      { name: '标的某某某项目A', value: 5969, rank: 1 },
      { name: '标的某某某项目B', value: 4569, rank: 2 },
      { name: '标的某某某项目C', value: 3969, rank: 3 },
      { name: '标的某某某项目D', value: 2969, rank: 4 },
      { name: '标的某某某项目E', value: 1969, rank: 5 },
      { name: '标的某某某项目F', value: 969, rank: 6 },
      { name: '标的某某某项目G', value: 869, rank: 7 },
      { name: '标的某某某项目H', value: 769, rank: 8 },
      { name: '标的某某某项目I', value: 669, rank: 9 },
      { name: '标的某某某项目J', value: 569, rank: 10 },
    ],
    premiumRateRanking: [
      { name: '标的某某某项目A', value: 95.5, rank: 1 },
      { name: '标的某某某项目B', value: 92.3, rank: 2 },
      { name: '标的某某某项目C', value: 89.7, rank: 3 },
      { name: '标的某某某项目D', value: 87.2, rank: 4 },
      { name: '标的某某某项目E', value: 85.8, rank: 5 },
      { name: '标的某某某项目F', value: 83.4, rank: 6 },
      { name: '标的某某某项目G', value: 81.9, rank: 7 },
      { name: '标的某某某项目H', value: 79.6, rank: 8 },
      { name: '标的某某某项目I', value: 77.3, rank: 9 },
      { name: '标的某某某项目J', value: 75.1, rank: 10 },
    ],
    assetDisposalRanking: [
      { name: '资产处置项目A', value: 88.9, rank: 1 },
      { name: '资产处置项目B', value: 86.5, rank: 2 },
      { name: '资产处置项目C', value: 84.2, rank: 3 },
      { name: '资产处置项目D', value: 82.7, rank: 4 },
      { name: '资产处置项目E', value: 80.3, rank: 5 },
      { name: '资产处置项目F', value: 78.9, rank: 6 },
      { name: '资产处置项目G', value: 76.4, rank: 7 },
      { name: '资产处置项目H', value: 74.8, rank: 8 },
      { name: '资产处置项目I', value: 72.5, rank: 9 },
      { name: '资产处置项目J', value: 70.2, rank: 10 },
    ],
  };

  /**
   * 获取KPI数据
   */
  getKPIData(): Promise<KPIData> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({ ...this.kpiData });
      }, 100);
    });
  }

  /**
   * 获取图表数据
   */
  getChartData(): Promise<ChartData> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({ ...this.chartData });
      }, 200);
    });
  }

  /**
   * 模拟数据更新
   */
  updateData(): void {
    // 随机更新KPI数据
    this.kpiData.totalAmount += Math.floor(Math.random() * 10000) - 5000;
    this.kpiData.premiumAmount += Math.floor(Math.random() * 100) - 50;
    this.kpiData.premiumRate += (Math.random() - 0.5) * 2;
    this.kpiData.totalTargets += Math.floor(Math.random() * 10) - 5;
    
    // 确保数据合理性
    this.kpiData.totalAmount = Math.max(0, this.kpiData.totalAmount);
    this.kpiData.premiumAmount = Math.max(0, this.kpiData.premiumAmount);
    this.kpiData.premiumRate = Math.max(0, Math.min(100, this.kpiData.premiumRate));
    this.kpiData.totalTargets = Math.max(0, this.kpiData.totalTargets);

    // 随机更新趋势数据
    this.chartData.trendData = this.chartData.trendData.map(item => ({
      ...item,
      value: Math.max(50, Math.min(100, item.value + (Math.random() - 0.5) * 10))
    }));

    // 随机更新排名数据
    this.chartData.premiumAmountRanking = this.chartData.premiumAmountRanking.map(item => ({
      ...item,
      value: Math.max(100, item.value + Math.floor(Math.random() * 200) - 100)
    }));
  }

  /**
   * 开始自动数据更新
   */
  startAutoUpdate(interval: number = 30000): NodeJS.Timeout {
    return setInterval(() => {
      this.updateData();
    }, interval);
  }

  /**
   * 停止自动数据更新
   */
  stopAutoUpdate(intervalId: NodeJS.Timeout): void {
    clearInterval(intervalId);
  }

  /**
   * 获取实时数据流（模拟WebSocket）
   */
  getRealtimeData(callback: (data: { kpi: KPIData; chart: ChartData }) => void): () => void {
    const interval = setInterval(async () => {
      this.updateData();
      const kpi = await this.getKPIData();
      const chart = await this.getChartData();
      callback({ kpi, chart });
    }, 5000);

    return () => clearInterval(interval);
  }

  /**
   * 格式化数字显示
   */
  formatNumber(num: number, decimals: number = 0): string {
    return num.toLocaleString('zh-CN', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    });
  }

  /**
   * 格式化百分比显示
   */
  formatPercentage(num: number, decimals: number = 2): string {
    return `${num.toFixed(decimals)}%`;
  }

  /**
   * 格式化金额显示
   */
  formatAmount(num: number, unit: string = '万元'): string {
    return `${this.formatNumber(num)}${unit}`;
  }
}

// 导出单例实例
export const dataService = new DataService();

// 导出数据服务类
export default DataService;
