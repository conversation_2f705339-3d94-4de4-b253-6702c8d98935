# 样式文件更新指南

## 概述

为了支持全屏和非全屏状态的样式切换，需要对现有的样式文件进行更新。目前您已经将样式分离到两个文件夹：

- `styles/fullScreen/` - 全屏状态样式（1658×994）
- `styles/notFullScreen/` - 非全屏状态样式（1658×821）

## 更新原理

通过CSS选择器的特异性来控制样式的应用：
- 全屏状态：`.visual-container.fullscreen .component-class`
- 非全屏状态：`.visual-container:not(.fullscreen) .component-class`

## 需要更新的文件

### fullScreen 文件夹
- ✅ `index.less` (已更新)
- ⏳ `AssetRanking.less`
- ⏳ `BottomCharts.less`
- ⏳ `ChartSection.less`
- ⏳ `CountTo.less`
- ⏳ `DataCards.less`
- ⏳ `MainKPICards.less`
- ⏳ `RankingSection.less`
- ⏳ `SmallCards.less`
- ⏳ `TargetRanking.less`
- ⏳ `VisualHeader.less`

### notFullScreen 文件夹
- ✅ `index.less` (已更新)
- ⏳ `AssetRanking.less`
- ⏳ `BottomCharts.less`
- ⏳ `ChartSection.less`
- ⏳ `CountTo.less`
- ⏳ `DataCards.less`
- ⏳ `MainKPICards.less`
- ⏳ `RankingSection.less`
- ⏳ `SmallCards.less`
- ⏳ `TargetRanking.less`
- ⏳ `VisualHeader.less`

## 更新步骤

### 1. 全屏样式文件更新

对于 `styles/fullScreen/` 文件夹中的每个组件样式文件：

**步骤：**
1. 在文件开头添加注释：
   ```less
   // 全屏状态下的样式 - 1658px × 994px
   ```

2. 将所有顶级CSS类选择器添加前缀 `.visual-container.fullscreen`

**示例：**

**更新前：**
```less
.header-content {
  padding: 20px;
  height: 88px;
  
  .title {
    font-size: 24px;
  }
}

.nav-button {
  width: 120px;
  height: 40px;
}
```

**更新后：**
```less
// 全屏状态下的样式 - 1658px × 994px
.visual-container.fullscreen .header-content {
  padding: 20px;
  height: 88px;
  
  .title {
    font-size: 24px;
  }
}

.visual-container.fullscreen .nav-button {
  width: 120px;
  height: 40px;
}
```

### 2. 非全屏样式文件更新

对于 `styles/notFullScreen/` 文件夹中的每个组件样式文件：

**步骤：**
1. 在文件开头添加注释：
   ```less
   // 非全屏状态下的样式 - 1658px × 821px
   ```

2. 将所有顶级CSS类选择器添加前缀 `.visual-container:not(.fullscreen)`

3. **重要：** 根据非全屏的尺寸需求调整具体的样式值

**示例：**

**更新前：**
```less
.header-content {
  padding: 20px;
  height: 88px;
}

.visual-footer {
  height: 277px;
}
```

**更新后：**
```less
// 非全屏状态下的样式 - 1658px × 821px
.visual-container:not(.fullscreen) .header-content {
  padding: 20px;
  height: 88px;
}

.visual-container:not(.fullscreen) .visual-footer {
  height: 233px; // 调整为适合821px高度的值
}
```

## 关键尺寸调整建议

### 非全屏模式需要调整的尺寸

由于从994px高度调整到821px高度，减少了173px，建议按比例调整：

1. **头部区域**：保持88px
2. **底部区域**：从277px调整到233px（减少44px）
3. **中间区域**：自动调整剩余空间
4. **组件内部间距**：可以适当减少10-20%

### 具体调整参考

```less
// 全屏 (994px 高度)
.visual-footer { height: 277px; }
.chart-card { height: 200px; }
.rank-item { height: 48px; }

// 非全屏 (821px 高度) 
.visual-footer { height: 233px; }  // 减少44px
.chart-card { height: 180px; }     // 减少20px
.rank-item { height: 44px; }       // 减少4px
```

## 注意事项

1. **只更新顶级选择器**：嵌套的选择器不需要添加前缀
2. **保持原有结构**：不要改变CSS的嵌套结构
3. **避免重复前缀**：如果选择器已经包含 `.visual-container`，不要重复添加
4. **测试验证**：更新后要在两种模式下测试样式效果

## 验证方法

更新完成后，可以通过以下方式验证：

1. **浏览器开发者工具**：检查元素的CSS规则是否正确应用
2. **切换全屏状态**：按F11或ESC键切换，观察样式变化
3. **控制台检查**：确保没有CSS语法错误

## 自动化工具

我已经创建了 `utils/styleUpdater.ts` 工具文件，包含了更新逻辑和验证方法，您可以参考其中的规则进行手动更新。

## 完成标记

更新完成后，请将上面的 ⏳ 标记改为 ✅，以便跟踪进度。
