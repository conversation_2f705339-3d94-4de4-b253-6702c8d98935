# 标的列表弹窗优化说明

## 问题背景

在自主竞价页面、我的待办、我的已办三个页面中都用到了查看/审核自主竞价弹窗，自主竞价中包含查看标的详情的功能。之前的实现是通过接口获取标的列表数据，但我们没有这个接口。实际上，我们获取到的竞价委托详情中已经包含了标的列表数据（`hgyAuction.hgyAuctionItemList`）。

## 解决方案

修改 `CommonAuctionItemsModal` 组件，让它能够直接接收标的列表数据作为props，而不是必须通过API获取。

## 修改内容

### 1. CommonAuctionItemsModal 组件修改

**文件**: `src/components/Audit/src/CommonAuctionItemsModal.vue`

#### 新增Props

```typescript
interface Props {
  open: boolean;
  recordId?: string | undefined;
  auctionItemList?: AuctionItem[]; // 新增：直接传入标的列表数据
}
```

#### 修改数据加载逻辑

```typescript
// 加载标的列表数据
async function loadAuctionItems() {
  // 优先使用传入的标的列表数据
  if (props.auctionItemList && props.auctionItemList.length > 0) {
    auctionItems.value = props.auctionItemList;
    return;
  }

  // 如果没有传入数据且有recordId，则调用API获取
  if (props.recordId) {
    await fetchAuctionItemsFromAPI();
  }
}
```

### 2. CommonSelfAudit 组件修改

**文件**: `src/components/Audit/src/CommonSelfAudit.vue`

#### 传递标的列表数据

```vue
<!-- 标的列表弹窗 -->
<CommonAuctionItemsModal
  v-model:open="auctionItemsVisible"
  :record-id="record?.id"
  :auction-item-list="auditData?.hgyAuction?.hgyAuctionItemList"
  @close="handleCloseAuctionItems"
/>
```

### 3. 我的已办页面修改

**文件**: `src/views/manageCenter/alreadyLog/index.vue`

#### 新增导入

```typescript
import { getAuditDetailByType } from '/@/api/manageCenter/audit';
import type { AuctionItem } from '/@/components/Audit/types';
```

#### 新增状态变量

```typescript
const currentAuctionItemList = ref<AuctionItem[]>([]);
```

#### 修改查看标的列表函数

```typescript
// 查看标的列表
async function handleViewAuctionItems(record: AlreadyLogRecord) {
  currentRecordId.value = record.id;

  // 获取详情数据以获取标的列表
  try {
    const response = await getAuditDetailByType(
      record.id,
      record.entrustType || 2, // 自主委托
      record.serviceType || 1 // 竞价委托
    );

    // 提取标的列表数据
    if (response?.hgyAuction?.hgyAuctionItemList) {
      currentAuctionItemList.value = response.hgyAuction.hgyAuctionItemList;
    } else {
      currentAuctionItemList.value = [];
    }
  } catch (error) {
    console.error('获取委托详情失败:', error);
    currentAuctionItemList.value = [];
  }

  itemsModalVisible.value = true;
}
```

#### 修改弹窗组件调用

```vue
<!-- 标的列表弹窗 -->
<CommonAuctionItemsModal
  v-model:open="itemsModalVisible"
  :record-id="currentRecordId"
  :auction-item-list="currentAuctionItemList"
  @close="itemsModalVisible = false"
/>
```

## 优化效果

### 1. 数据来源优化

- **之前**: 必须调用专门的标的列表接口
- **现在**: 优先使用已有的详情数据中的标的列表，减少不必要的接口调用

### 2. 兼容性保持

- 保持了原有的API调用方式作为备用方案
- 对于没有传入标的列表数据的情况，仍然会尝试调用API

### 3. 性能提升

- 减少了重复的网络请求
- 利用已有数据，提升用户体验

## 涉及页面

1. **自主竞价页面** (`src/views/orderManage/autonomouslyBidding/index.vue`)
   - 通过 DetailViewModal → CommonSelfAudit → CommonAuctionItemsModal 的调用链
   - 自动使用详情数据中的标的列表

2. **我的待办** (`src/views/manageCenter/backLog/index.vue`)
   - 该页面使用 CommonAuditModal 进行审核，不直接使用标的列表弹窗
   - 无需修改

3. **我的已办** (`src/views/manageCenter/alreadyLog/index.vue`)
   - 直接调用 CommonAuctionItemsModal
   - 修改为先获取详情数据，然后传递标的列表

## 数据结构

标的列表数据结构 (`AuctionItem`):

```typescript
interface AuctionItem {
  id?: string;
  auctionName?: string; // 关联拍卖会
  itemTitle?: string; // 标的标题
  itemType?: number; // 标的类型
  startPrice?: number; // 起拍价
  appraisalPrice?: number; // 评估价格
  deposit?: number; // 保证金
  quantity?: number; // 标的数量
  unit?: string; // 单位
  province?: string; // 省份
  city?: string; // 城市
  district?: string; // 区域
  address?: string; // 详细地址
  description?: string; // 标的描述
  auctionMode?: number; // 拍卖方式
}
```

## 最新优化 (2024-01-XX)

### 1. 样式优化

- **上下边距**: 为弹窗内容添加了20px的上下边距，避免内容贴边显示
- **富文本样式**: 为标的介绍添加了专门的富文本样式，支持段落、图片、列表等格式

### 2. 字段名称修改

- **标的描述** → **标的介绍**: 更符合业务语义
- **富文本展示**: 使用 `v-html` 指令展示富文本内容

### 3. 附件功能

- **附件显示**: 支持显示标的附件列表 (`hgyAttachmentList`)
- **文件预览**: 点击附件可以预览文件内容
- **附件样式**: 美观的附件卡片样式，支持悬停效果

### 4. 类型定义更新

#### AuctionItem 接口新增字段

```typescript
export interface AuctionItem {
  // ... 原有字段
  hgyAttachmentList?: AttachmentInfo[]; // 标的附件列表
}

export interface AttachmentInfo {
  id?: string;
  bizType?: string;
  fileName?: string;
  filePath?: string;
  fileSize?: number;
  fileType?: string;
  originalName?: string;
}
```

### 5. 新增功能

#### 文件预览功能

```typescript
// 预览文件
function handlePreviewFile(attachment: AttachmentInfo) {
  if (!attachment.filePath) {
    message.warning('文件路径不存在');
    return;
  }

  openPreview({
    fileName: attachment.fileName || attachment.originalName || '附件',
    filePath: attachment.filePath,
    fileType: attachment.fileType,
    fileSize: attachment.fileSize,
  });
}
```

#### 富文本内容展示

```vue
<div class="field-value rich-text-content" v-html="item.description"></div>
```

#### 附件列表展示

```vue
<div class="attachment-list">
  <div
    v-for="(attachment, attachIndex) in item.hgyAttachmentList"
    :key="attachIndex"
    class="attachment-item"
    @click="handlePreviewFile(attachment)"
  >
    <Icon icon="ant-design:file-outlined" class="attachment-icon" />
    <span class="attachment-name">{{ attachment.fileName || attachment.originalName || '附件' }}</span>
  </div>
</div>
```

### 6. 样式特性

#### 弹窗边距

```less
.auction-items-modal {
  padding: 20px 0; // 添加上下边距
}
```

#### 富文本样式

```less
.rich-text-content {
  line-height: 1.6;

  :deep(p) {
    margin-bottom: 8px;
    &:last-child {
      margin-bottom: 0;
    }
  }

  :deep(img) {
    max-width: 100%;
    height: auto;
  }

  :deep(ul),
  :deep(ol) {
    padding-left: 20px;
    margin-bottom: 8px;
  }
}
```

#### 附件样式

```less
.attachment-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;

  .attachment-item {
    display: flex;
    align-items: center;
    padding: 6px 12px;
    background: #f5f5f5;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      background: #e6f7ff;
      border-color: #1890ff;
    }
  }
}
```

## 注意事项

1. 确保 `hgyAuction.hgyAuctionItemList` 数据结构与 `AuctionItem` 接口匹配
2. 如果详情数据中没有标的列表，组件会回退到API调用方式
3. 保持向后兼容性，现有的使用方式仍然有效
4. **富文本安全**: 使用 `v-html` 时确保内容来源可信，避免XSS攻击
5. **附件路径**: 确保附件的 `filePath` 字段正确，否则无法预览
6. **文件预览**: 依赖 `FilePreview` 组件和 `useFilePreview` hook
