.visual-header {
  background: url('https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/header_1754987780270.png') no-repeat center center;
  background-size: cover;
  height: 88px;
  width: 100%;
}

.header-content {
  position: relative;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 2;
  padding: 0 29px 0 27px;
}

.header-left {
  display: flex;
  gap: 20px;
}

.header-center {
  .main-title {
    color: #ffffff;
    font-size: 36px;
    margin: 0;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
    background: #fff;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-top: 10px;
    font-family: 'YouSheBiaoTiHei';
  }
}

.header-right {
  display: flex;
  gap: 20px;
  align-items: center;
  width: 380px;
  justify-content: end;

  .time-info {
    color: #2bccff !important;
    font-size: 16px;
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
  }
}

.nav-button {
  position: relative;
  width: 135px;
  height: 35px;
  cursor: pointer;
  transition: all 0.3s ease;

  background: linear-gradient(135deg, #2bccff 0%, #1a8fcc 50%, #2bccff 100%);

  clip-path: polygon(0 0, ~'calc(100% - 8px)' 0, 100% 8px, 100% 100%, 8px 100%, 0 ~'calc(100% - 8px)');

  /* 外发光效果 */
  box-shadow:
    0 0 10px rgba(19, 230, 219, 0.4),
    0 0 20px rgba(19, 230, 219, 0.2);

  /* 添加发光动画 */
  animation: buttonGlow 3s ease-in-out infinite;

  &:hover {
    transform: translateY(-2px);
    box-shadow:
      0 0 15px rgba(19, 230, 219, 0.6),
      0 0 25px rgba(19, 230, 219, 0.3);

    .button-text {
      background: linear-gradient(135deg, rgba(0, 76, 102, 0.9) 0%, rgba(0, 60, 80, 1) 50%, rgba(0, 40, 60, 0.9) 100%);

      box-shadow:
        inset 0 0 15px rgba(19, 230, 219, 0.4),
        inset 0 0 25px rgba(19, 230, 219, 0.2);
    }
  }

  .button-text {
    position: absolute;
    top: 2px;
    left: 2px;
    right: 2px;
    bottom: 2px;

    /* 内部容器背景 */
    background: linear-gradient(135deg, rgba(0, 76, 102, 0.8) 0%, rgba(0, 60, 80, 0.9) 50%, rgba(0, 40, 60, 0.8) 100%);

    /* 内部容器的切角效果（比外部小2px） */
    clip-path: polygon(0 0, ~'calc(100% - 6px)' 0, 100% 6px, 100% 100%, 6px 100%, 0 ~'calc(100% - 6px)');

    /* 内发光效果 */
    box-shadow:
      inset 0 0 10px rgba(19, 230, 219, 0.3),
      inset 0 0 20px rgba(19, 230, 219, 0.1);

    /* 文字内容样式 */
    color: #ffffff;
    font-size: 14px;
    text-shadow: 0 0 4px rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
  }

  /* 不同按钮的特殊尺寸 */
  &.address-select {
    width: 225px;
  }

  /* 级联选择器样式重置 */
  :deep(.ant-cascader) {
    width: 100%;
    height: 100%;

    .ant-select-selector {
      background: transparent !important;
      border: none !important;
      box-shadow: none !important;
      padding: 0 !important;
      height: 100% !important;

      .ant-select-selection-search {
        display: none !important;
      }

      .ant-select-selection-item,
      .ant-select-selection-placeholder {
        display: none !important;
      }
    }

    .ant-select-arrow {
      display: none !important;
    }
  }
}

/* 发光动画效果 */
@keyframes buttonGlow {
  0% {
    box-shadow:
      0 0 10px rgba(43, 204, 255, 0.4),
      0 0 20px rgba(43, 204, 255, 0.2);
  }
  50% {
    box-shadow:
      0 0 15px rgba(43, 204, 255, 0.6),
      0 0 25px rgba(43, 204, 255, 0.3);
  }
  100% {
    box-shadow:
      0 0 10px rgba(43, 204, 255, 0.4),
      0 0 20px rgba(43, 204, 255, 0.2);
  }
}

/* 级联选择器下拉框样式 */
:deep(.ant-cascader-dropdown) {
  background: rgba(43, 204, 255, 0.8) !important;
  border: 1px solid #2bccff !important;
  border-radius: 8px !important;
  box-shadow: 0 0 20px rgba(43, 204, 255, 0.3) !important;
  backdrop-filter: blur(10px) !important;

  .ant-cascader-menu {
    background: transparent !important;
    border-right: 1px solid rgba(19, 230, 219, 0.2) !important;

    .ant-cascader-menu-item {
      color: #fff !important;
      background: transparent !important;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;

      &:hover {
        background: rgba(19, 230, 219, 0.2) !important;
        color: #fff !important;
      }

      &.ant-cascader-menu-item-active {
        background: rgba(19, 230, 219, 0.3) !important;
        color: #fff !important;
      }

      &.ant-cascader-menu-item-selected {
        background: rgba(19, 230, 219, 0.4) !important;
        color: #fff !important;
      }
    }
  }

  .ant-cascader-menu:last-child {
    border-right: none !important;
  }
}

/* 搜索框样式 */
:deep(.ant-cascader-dropdown .ant-input) {
  background: rgba(0, 76, 102, 0.6) !important;
  border: 1px solid rgba(43, 204, 255, 0.3) !important;
  color: #fff !important;

  &::placeholder {
    color: rgba(255, 255, 255, 0.6) !important;
  }

  &:focus {
    border-color: #2bccff !important;
    box-shadow: 0 0 5px rgba(43, 204, 255, 0.5) !important;
    background: rgba(0, 76, 102, 0.8) !important;
  }
}

/* 级联选择器下拉框样式 */
:deep(.ant-cascader-dropdown) {
  background: rgba(43, 204, 255, 0.8) !important;
  border: 1px solid #2bccff !important;
  border-radius: 8px !important;
  box-shadow: 0 0 20px rgba(43, 204, 255, 0.3) !important;
  backdrop-filter: blur(10px) !important;

  // 展开图标颜色
  .ant-cascader-menu-item-expand-icon {
    color: #fff !important;
  }

  .ant-cascader-menu {
    background: transparent !important;
    border-right: 1px solid rgba(19, 230, 219, 0.2) !important;

    .ant-cascader-menu-item {
      color: #fff !important;
      background: transparent !important;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;

      &:hover {
        background: rgba(19, 230, 219, 0.2) !important;
        color: #fff !important;
      }

      &.ant-cascader-menu-item-active {
        background: rgba(19, 230, 219, 0.3) !important;
        color: #fff !important;
      }

      &.ant-cascader-menu-item-selected {
        background: rgba(19, 230, 219, 0.4) !important;
        color: #fff !important;
      }
    }
  }

  .ant-cascader-menu:last-child {
    border-right: none !important;
  }
}
