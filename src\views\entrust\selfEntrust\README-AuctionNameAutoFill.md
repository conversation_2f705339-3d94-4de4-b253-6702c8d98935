# 自主委托 - 拍卖会名称自动填充功能

## 功能说明

在自主委托流程中，第二步的标的信息需要填写"关联拍卖会"字段。为了提升用户体验，现在实现了自动填充功能：当用户在第一步填写了拍卖会名称后，第二步中新增的标的会自动填充该拍卖会名称。

## 实现方案

### 1. Step2 组件修改

**文件**: `src/views/entrust/selfEntrust/components/Step2.vue`

#### Props 扩展
```typescript
interface Props {
  modelValue: {
    auctionItems: Array<{
      auctionName: string;
      // ... 其他字段
    }>;
  };
  serviceType?: number;
  stepOneData?: {
    auctionInfo: {
      auctionName: string;
    };
  }; // 新增：第一步的数据，用于自动填充拍卖会名称
}
```

#### 自动填充逻辑

1. **新增标的时自动填充**：
```typescript
// 添加标的
const addItem = () => {
  const newItem = {
    auctionName: props.stepOneData?.auctionInfo?.auctionName || '', // 自动填充第一步的拍卖会名称
    itemTitle: '',
    itemType: 1,
    // ... 其他默认值
  };
  formData.auctionItems.push(newItem);
};
```

2. **监听第一步数据变化**：
```typescript
// 监听第一步拍卖会名称变化，自动填充到标的中
watch(
  () => props.stepOneData?.auctionInfo?.auctionName,
  (newAuctionName) => {
    if (newAuctionName) {
      // 为所有拍卖会名称为空的标的自动填充
      formData.auctionItems.forEach((item) => {
        if (!item.auctionName) {
          item.auctionName = newAuctionName;
        }
      });
    }
  },
  { immediate: true }
);
```

### 2. 父组件修改

**文件**: `src/views/entrust/selfEntrust/index.vue`

#### 数据传递
```vue
<!-- 发布竞价标的：显示标的信息 -->
<Step2 
  v-if="serviceType === 1" 
  ref="step2Ref" 
  v-model="stepTwoData" 
  :service-type="serviceType" 
  :step-one-data="stepOneData" 
/>
```

## 功能特性

### 1. 智能填充
- **新增标的**: 点击"新增"按钮时，新标的的"关联拍卖会"字段会自动填充第一步中的拍卖会名称
- **已有标的**: 如果已有标的的拍卖会名称为空，当第一步的拍卖会名称变化时会自动填充

### 2. 用户友好
- **非强制性**: 用户仍然可以手动修改关联拍卖会名称
- **智能判断**: 只有当标的的拍卖会名称为空时才会自动填充，不会覆盖用户已填写的内容

### 3. 实时同步
- **即时响应**: 当用户在第一步修改拍卖会名称时，第二步中空的拍卖会名称字段会立即更新
- **数据一致性**: 确保第一步和第二步的拍卖会名称保持一致

## 数据流

```
第一步 (Step1)
├── 用户填写拍卖会名称
├── stepOneData.auctionInfo.auctionName 更新
└── 传递给 Step2

第二步 (Step2)
├── 接收 stepOneData
├── 监听 auctionName 变化
├── 新增标的时自动填充
└── 已有空标的自动填充
```

## 使用场景

### 场景1：正常流程
1. 用户在第一步填写拍卖会名称："2024年第一期拍卖会"
2. 进入第二步，默认标的的关联拍卖会自动填充为："2024年第一期拍卖会"
3. 用户点击"新增"添加第二个标的，新标的也自动填充相同名称

### 场景2：修改拍卖会名称
1. 用户在第二步发现需要修改拍卖会名称
2. 返回第一步，将拍卖会名称改为："2024年第二期拍卖会"
3. 再次进入第二步，所有空的关联拍卖会字段自动更新为新名称

### 场景3：个别标的使用不同拍卖会
1. 用户在第一步填写："2024年第一期拍卖会"
2. 第二步中，第一个标的自动填充该名称
3. 用户手动将第二个标的的拍卖会名称改为："2024年特殊拍卖会"
4. 当第一步名称变化时，第一个标的会更新，第二个标的保持不变

## 技术细节

### 1. 响应式数据
- 使用 Vue 3 的 `watch` API 监听数据变化
- `immediate: true` 确保组件初始化时就执行填充逻辑

### 2. 条件判断
```typescript
// 只有当标的的拍卖会名称为空时才自动填充
if (!item.auctionName) {
  item.auctionName = newAuctionName;
}
```

### 3. 可选链操作符
```typescript
// 安全地访问嵌套属性，避免运行时错误
props.stepOneData?.auctionInfo?.auctionName || ''
```

## 兼容性

- **向后兼容**: 不影响现有功能，如果没有传递 `stepOneData`，组件仍正常工作
- **可选功能**: `stepOneData` 是可选的 prop，不会破坏现有的组件调用
- **渐进增强**: 在现有功能基础上增加便利性，不改变核心逻辑

## 注意事项

1. **数据安全**: 使用可选链操作符避免访问未定义属性导致的错误
2. **用户意图**: 只对空字段进行自动填充，尊重用户已填写的内容
3. **性能考虑**: 使用 `watch` 的 `immediate` 选项，避免不必要的重复执行
4. **类型安全**: 完整的 TypeScript 类型定义，确保编译时类型检查

## 测试建议

1. **基本功能测试**:
   - 第一步填写拍卖会名称，第二步验证自动填充
   - 新增标的验证自动填充

2. **边界情况测试**:
   - 第一步拍卖会名称为空时的处理
   - 第二步已有内容时不被覆盖

3. **交互测试**:
   - 来回切换步骤时数据的一致性
   - 修改第一步名称后第二步的响应
