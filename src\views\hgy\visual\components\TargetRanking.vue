<template>
  <div class="target-ranking">
    <!-- 背景 -->
    <div class="ranking-bg"></div>

    <!-- 内容 -->
    <div class="ranking-content">
      <!-- 总标题 -->
      <div class="main-title">标的数据排名</div>

      <!-- 标的溢价额排名 -->
      <div class="ranking-section">
        <div class="section-header">
          <div class="section-divider left"></div>
          <div class="section-title">标的溢价额排名</div>
          <div class="section-divider right"></div>
        </div>
        <div class="ranking-list">
          <div v-for="(item, index) in premiumAmountData" :key="index" class="ranking-item">
            <!-- 奖牌图标 -->
            <div class="medal-icon" :class="`rank-${index + 1}`">
              <div class="medal-bg"></div>
              <span class="rank-number">{{ index + 1 }}</span>
            </div>

            <!-- 项目名称 -->
            <div class="project-name">{{ item.name }}</div>

            <!-- 进度条 -->
            <div class="progress-container">
              <div class="progress-bar">
                <div
                  class="progress-fill"
                  :style="{
                    width: animatedPremiumAmountData[index] ? `${(item.value / maxPremiumAmount) * 100}%` : '0%',
                    transitionDelay: `${index * 200}ms`,
                  }"
                >
                  <div class="progress-diamond"></div>
                </div>
              </div>
              <div class="progress-value">
                <span class="animated-number">{{ animatedPremiumAmountValues[index] || 0 }}</span>
                <span class="progress-unit">万</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 标的溢价率排名 -->
      <div class="ranking-section">
        <div class="section-header width-limit">
          <div class="section-divider left"></div>
          <div class="section-title">标的溢价率排名</div>
          <div class="section-divider right"></div>
        </div>
        <div class="ranking-list">
          <div v-for="(item, index) in premiumRateData" :key="index" class="ranking-item">
            <!-- 奖牌图标 -->
            <div class="medal-icon" :class="`rank-${index + 1}`">
              <div class="medal-bg"></div>
              <span class="rank-number">{{ index + 1 }}</span>
            </div>

            <!-- 项目名称 -->
            <div class="project-name">{{ item.name }}</div>

            <!-- 进度条 -->
            <div class="progress-container">
              <div class="progress-bar">
                <div
                  class="progress-fill"
                  :style="{
                    width: animatedPremiumRateData[index] ? `${item.value}%` : '0%',
                    transitionDelay: `${index * 200}ms`,
                  }"
                >
                  <div class="progress-diamond"></div>
                </div>
              </div>
              <div class="progress-value">
                <span class="animated-number">{{ animatedPremiumRateValues[index] || 0 }}</span>
                <span class="progress-unit">%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue';

  // 模拟数据
  const premiumAmountData = ref([
    { name: '标的某某某项目A', value: 5969 },
    { name: '标的某某某项目B', value: 4569 },
    { name: '标的某某某项目C', value: 3969 },
    { name: '标的某某某项目D', value: 2969 },
    { name: '标的某某某项目E', value: 1969 },
    { name: '标的某某某项目F', value: 969 },
    { name: '标的某某某项目G', value: 869 },
  ]);

  const premiumRateData = ref([
    { name: '标的某某某项目A', value: 95.5 },
    { name: '标的某某某项目B', value: 92.3 },
    { name: '标的某某某项目C', value: 89.7 },
    { name: '标的某某某项目D', value: 87.2 },
    { name: '标的某某某项目E', value: 85.8 },
    { name: '标的某某某项目F', value: 83.4 },
    { name: '标的某某某项目G', value: 81.9 },
  ]);

  // 动画控制状态
  const animatedPremiumAmountData = ref<boolean[]>(new Array(premiumAmountData.value.length).fill(false));
  const animatedPremiumRateData = ref<boolean[]>(new Array(premiumRateData.value.length).fill(false));

  // 动画数值
  const animatedPremiumAmountValues = ref<number[]>(new Array(premiumAmountData.value.length).fill(0));
  const animatedPremiumRateValues = ref<number[]>(new Array(premiumRateData.value.length).fill(0));

  // 计算最大溢价额用于进度条比例
  const maxPremiumAmount = computed(() => {
    return Math.max(...premiumAmountData.value.map((item) => item.value));
  });

  // 数字动画函数
  const animateNumber = (startValue: number, endValue: number, duration: number, callback: (value: number) => void) => {
    const startTime = Date.now();
    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 使用缓动函数
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const currentValue = startValue + (endValue - startValue) * easeOutQuart;

      callback(Math.round(currentValue * 10) / 10); // 保留一位小数

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };
    requestAnimationFrame(animate);
  };

  // 启动动画
  const startAnimations = () => {
    // 延迟启动，让组件完全渲染
    setTimeout(() => {
      // 启动溢价额动画
      premiumAmountData.value.forEach((item, index) => {
        setTimeout(() => {
          animatedPremiumAmountData.value[index] = true;

          // 启动数字动画
          animateNumber(0, item.value, 2000, (value) => {
            animatedPremiumAmountValues.value[index] = value;
          });
        }, index * 200);
      });

      // 启动溢价率动画
      premiumRateData.value.forEach((item, index) => {
        setTimeout(() => {
          animatedPremiumRateData.value[index] = true;

          // 启动数字动画
          animateNumber(0, item.value, 2000, (value) => {
            animatedPremiumRateValues.value[index] = value;
          });
        }, index * 200);
      });
    }, 500); // 延迟500ms开始动画
  };

  onMounted(() => {
    startAnimations();
  });
</script>

<style lang="less" scoped>
  @import '../styles/fullScreen/TargetRanking.less';
</style>
