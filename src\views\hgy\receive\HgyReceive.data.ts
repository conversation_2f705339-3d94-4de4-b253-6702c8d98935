import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '发送者用户名',
    align:"center",
    dataIndex: 'sendUserName'
   },
   {
    title: '接收者用户名',
    align:"center",
    dataIndex: 'receUserName'
   },
   {
    title: '服务单id',
    align:"center",
    dataIndex: 'enterustOrderId'
   },
   {
    title: '项目名称',
    align:"center",
    dataIndex: 'projectName'
   },
   {
    title: '发送状态',
    align:"center",
    dataIndex: 'sendStatus'
   },
   {
    title: '客户端类型 pc:电脑端 app:手机端 h5:移动网页端',
    align:"center",
    dataIndex: 'sendSource'
   },
   {
    title: '消息内容',
    align:"center",
    dataIndex: 'message',
   },
   {
    title: '发送时间',
    align:"center",
    dataIndex: 'createTime'
   },
   {
    title: '租户id',
    align:"center",
    dataIndex: 'tenantId'
   },
   {
    title: '回复状态(0:未回复,1:已回复)',
    align:"center",
    dataIndex: 'replyStatus_dictText'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: "项目名称",
    field: "projectName",
    component: 'JInput',
  },
	{
      label: "消息内容",
      field: 'message',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "回复状态(0:未回复,1:已回复)",
      field: 'replyStatus',
      component: 'JSelectMultiple',
      componentProps:{
          dictCode:"replyStatusDict"
      },
      //colProps: {span: 6},
 	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '消息内容',
    field: 'message',
    component: 'JEditor',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入消息内容!'},
          ];
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  sendUserName: {title: '发送者用户名',order: 0,view: 'text', type: 'string',},
  receUserName: {title: '接收者用户名',order: 1,view: 'text', type: 'string',},
  enterustOrderId: {title: '服务单id',order: 2,view: 'text', type: 'string',},
  projectName: {title: '项目名称',order: 3,view: 'text', type: 'string',},
  sendStatus: {title: '发送状态',order: 4,view: 'number', type: 'number',},
  sendSource: {title: '客户端类型 pc:电脑端 app:手机端 h5:移动网页端',order: 5,view: 'text', type: 'string',},
  message: {title: '消息内容',order: 6,view: 'umeditor', type: 'string',},
  createTime: {title: '发送时间',order: 7,view: 'datetime', type: 'string',},
  tenantId: {title: '租户id',order: 9,view: 'number', type: 'number',},
  replyStatus: {title: '回复状态(0:未回复,1:已回复)',order: 10,view: 'number', type: 'number',dictCode: 'replyStatusDict',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}