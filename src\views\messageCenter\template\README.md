# 消息模板页面

## 更新日志

### 2024-12-19 代码重构 - 组件拆分

将原本的单一文件拆分为多个组件，提高代码可维护性：

#### 文件结构

```
src/views/messageCenter/template/
├── index.vue                    # 主页面（表格展示和操作）
├── components/
│   ├── TemplateModal.vue       # 新增/编辑/查看弹窗组件
│   ├── SendTestModal.vue       # 发送测试弹窗组件
│   └── UserSelectModal.vue     # 用户选择弹窗组件
└── README.md
```

#### 组件职责分工

- **index.vue**: 主表格展示、筛选、操作按钮
- **TemplateModal.vue**: 模板的新增、编辑、查看功能
- **SendTestModal.vue**: 发送测试功能，包含用户选择
- **UserSelectModal.vue**: 用户选择表格和多选功能

#### 重构优势

1. **代码分离**: 每个组件职责单一，便于维护
2. **复用性**: 组件可以在其他地方复用
3. **可读性**: 主文件代码量减少，逻辑更清晰
4. **类型安全**: 每个组件都有明确的Props和Emits定义

### 2024-12-19 新增用户选择功能

在发送测试弹窗中新增了消息接收方选择功能：

1. **消息接收方输入框**：
   - 只读输入框，显示已选择的用户信息
   - 右侧有"选择"按钮，点击打开用户选择弹窗

2. **用户选择弹窗**：
   - 使用 CustomModal 自定义弹窗组件
   - 显示用户列表表格，支持多选
   - 筛选条件：账号、姓名
   - 使用系统用户管理接口：`/sys/user/customQueryPageList`

3. **发送接口数据格式**：

   ```json
   {
     "templateCode": "system_maintenance_notify",
     "templateName": "系统维护通知",
     "templateContent": "<h1>系统维护通知！！！</h1>",
     "testData": "{}",
     "msgType": "system",
     "receiver": "test072801,devlopUser"
   }
   ```

4. **技术实现**：
   - 使用 BasicTable 组件展示用户列表
   - 支持复选框多选用户
   - 用户头像显示（如果有的话）
   - 选择后显示格式：`姓名(账号), 姓名(账号)`

---

## 功能概述

这是一个基于现有消息模板功能重新实现的页面，使用了自定义的弹窗组件和富文本编辑器。

## 主要功能

### 1. 列表展示

- 模板标题
- 模板编码
- 模板类型（文本模板/富文本模板）
- 模板内容（截取前50字符显示）
- 是否应用状态
- 创建时间

### 2. 筛选功能

- 按模板标题筛选
- 按模板编码筛选
- 按模板类型筛选

### 3. 操作功能

- **新增**：点击新增按钮打开弹窗
- **查看**：查看模板详情（只读模式）
- **编辑**：编辑模板信息（已应用的模板不能编辑）
- **删除**：删除模板（已应用的模板不能删除）
- **应用/停用**：切换模板的应用状态
- **发送**：发送测试消息

### 4. 弹窗表单

- 使用 CustomModal 自定义弹窗组件
- 表单字段：
  - 模板标题（必填）
  - 模板编码（必填，编辑时不可修改）
  - 模板类型（必填）
    - 1: 文本模板 - 使用多行文本框
    - 2: 富文本模板 - 使用 JEditorTiptap 富文本编辑器
  - 是否应用（开关组件）
  - 模板内容（必填，根据模板类型切换输入方式）

### 5. 发送测试功能

- 独立的发送测试弹窗
- 显示模板信息
- 输入测试数据（JSON格式）
- 选择消息类型（系统消息/用户消息）
- **新增**：选择消息接收方（支持多选用户）

## 技术实现

### 组件使用

- `BasicTable`: 表格组件
- `CustomModal`: 自定义弹窗组件
- `JEditorTiptap`: 富文本编辑器组件
- `TableAction`: 表格操作按钮组件

### API接口

复用现有的消息模板API：

- `list`: 获取模板列表
- `saveOrUpdate`: 保存或更新模板
- `deleteBatch`: 删除模板
- `sendMessageTest`: 发送测试消息
- **新增**：`getUserList`: 获取用户列表（`/sys/user/customQueryPageList`）

### 数据字段

```json
{
  "id": "模板ID",
  "templateName": "模板标题",
  "templateCode": "模板编码",
  "templateType": "模板类型(1:文本,2:富文本)",
  "useStatus": "是否应用(0:否,1:是)",
  "templateContent": "模板内容"
}
```

## 样式特点

- 参考 entrustBidding 页面的样式风格
- 响应式布局
- 模板内容预览截取显示
- 状态标签颜色区分
- 表格操作按钮根据状态动态显示

## 注意事项

1. 已应用的模板（useStatus=1）不能编辑和删除
2. 模板类型切换时会清空模板内容
3. 发送测试时需要验证JSON格式
4. 编辑模式下模板编码不可修改
5. **新增**：发送测试时必须选择消息接收方
