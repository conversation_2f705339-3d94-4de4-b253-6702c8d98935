<template>
  <div class="responsive-test-page">
    <div class="test-header">
      <h1>响应式数据大屏测试页面</h1>
      <div class="screen-info">
        <div class="info-item">
          <span class="label">屏幕尺寸:</span>
          <span class="value">{{ screenSize.width }} × {{ screenSize.height }}</span>
        </div>
        <div class="info-item">
          <span class="label">当前断点:</span>
          <span class="value">{{ breakpoint }}</span>
        </div>
        <div class="info-item">
          <span class="label">设备类型:</span>
          <span class="value">{{ deviceType }}</span>
        </div>
        <div class="info-item">
          <span class="label">宽高比:</span>
          <span class="value">{{ screenSize.aspectRatio.toFixed(2) }}</span>
        </div>
      </div>
    </div>
    
    <div class="test-controls">
      <a-button type="primary" @click="openResponsiveDashboard">
        打开响应式数据大屏
      </a-button>
      <a-button @click="openResponsiveDashboardFullscreen">
        全屏打开响应式数据大屏
      </a-button>
      <a-button @click="openOriginalDashboard">
        打开原版数据大屏（对比）
      </a-button>
    </div>
    
    <div class="test-grid">
      <div class="test-card">
        <h3>布局配置</h3>
        <div class="config-list">
          <div class="config-item">
            <span class="key">网格列数:</span>
            <span class="value">{{ layoutConfig.columns }}</span>
          </div>
          <div class="config-item">
            <span class="key">间距:</span>
            <span class="value">{{ layoutConfig.gap }}</span>
          </div>
          <div class="config-item">
            <span class="key">内边距:</span>
            <span class="value">{{ layoutConfig.padding }}</span>
          </div>
          <div class="config-item">
            <span class="key">基础字体:</span>
            <span class="value">{{ layoutConfig.fontSize.base }}</span>
          </div>
        </div>
      </div>
      
      <div class="test-card">
        <h3>断点测试</h3>
        <div class="breakpoint-list">
          <div 
            v-for="(width, bp) in BREAKPOINTS" 
            :key="bp"
            class="breakpoint-item"
            :class="{ active: matchBreakpoint(bp) }"
          >
            <span class="bp-name">{{ bp }}</span>
            <span class="bp-width">{{ width }}px+</span>
            <span class="bp-status">{{ matchBreakpoint(bp) ? '✓' : '✗' }}</span>
          </div>
        </div>
      </div>
      
      <div class="test-card">
        <h3>响应式值测试</h3>
        <div class="responsive-values">
          <div class="value-item">
            <span class="key">响应式字体:</span>
            <span class="value" :style="{ fontSize: getResponsiveValue({ xs: '12px', sm: '14px', md: '16px', lg: '18px', xl: '20px' }) }">
              示例文字
            </span>
          </div>
          <div class="value-item">
            <span class="key">响应式间距:</span>
            <div class="spacing-demo" :style="{ padding: getResponsiveValue({ xs: '8px', sm: '12px', md: '16px', lg: '20px', xl: '24px' }) }">
              间距示例
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="test-preview">
      <h3>组件预览</h3>
      <div class="preview-grid">
        <!-- 小卡片预览 -->
        <div class="preview-item">
          <h4>小卡片网格</h4>
          <div class="mini-cards-grid">
            <div v-for="i in 10" :key="i" class="mini-card">
              <div class="mini-icon"></div>
              <div class="mini-info">
                <div class="mini-label">指标{{ i }}</div>
                <div class="mini-value">{{ (Math.random() * 1000).toFixed(0) }}</div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- KPI卡片预览 -->
        <div class="preview-item">
          <h4>KPI指标卡片</h4>
          <div class="mini-kpi-grid">
            <div v-for="(kpi, index) in ['成交总额', '溢价总额', '总溢价率']" :key="index" class="mini-kpi-card">
              <div class="mini-kpi-icon"></div>
              <div class="mini-kpi-info">
                <div class="mini-kpi-label">{{ kpi }}</div>
                <div class="mini-kpi-value">{{ (Math.random() * 10000).toFixed(2) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { useResponsiveLayout } from './composables/useResponsiveLayout';

const router = useRouter();

// 使用响应式布局Hook
const {
  screenSize,
  breakpoint,
  deviceType,
  layoutConfig,
  matchBreakpoint,
  getResponsiveValue,
  BREAKPOINTS,
} = useResponsiveLayout();

// 打开响应式数据大屏
const openResponsiveDashboard = () => {
  router.push('/hgy/visual-responsive');
};

// 全屏打开响应式数据大屏
const openResponsiveDashboardFullscreen = () => {
  router.push('/hgy/visual-responsive?fullscreen=true');
};

// 打开原版数据大屏
const openOriginalDashboard = () => {
  router.push('/hgy/visual');
};
</script>

<style lang="less" scoped>
@import './styles/responsive-variables.less';
@import './styles/responsive-mixins.less';

.responsive-test-page {
  padding: var(--spacing-lg);
  min-height: 100vh;
  background: var(--bg-dark);
  color: var(--text-light);
}

.test-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  
  h1 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
    .text-glow(var(--primary-color), 12px, 0.6);
  }
}

.screen-info {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
  
  .info-item {
    background: var(--card-bg);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-light);
    
    .label {
      color: var(--text-secondary);
      margin-right: var(--spacing-xs);
    }
    
    .value {
      color: var(--primary-color);
      font-weight: bold;
    }
  }
}

.test-controls {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
  flex-wrap: wrap;
}

.test-grid {
  .responsive-grid(3, var(--spacing-lg));
  margin-bottom: var(--spacing-xl);
}

.test-card {
  background: var(--card-bg);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  
  h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    text-align: center;
  }
}

.config-list, .responsive-values {
  .config-item, .value-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xs) 0;
    border-bottom: 1px solid var(--border-light);
    
    &:last-child {
      border-bottom: none;
    }
    
    .key {
      color: var(--text-secondary);
    }
    
    .value {
      color: var(--text-light);
      font-weight: bold;
    }
  }
}

.breakpoint-list {
  .breakpoint-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    margin-bottom: var(--spacing-xs);
    border-radius: var(--radius-sm);
    background: var(--bg-dark-40);
    transition: all var(--duration-normal) ease;
    
    &.active {
      background: var(--primary-color-10);
      border: 1px solid var(--primary-color);
      .glow-effect(var(--primary-color), 8px, 0.3);
    }
    
    .bp-name {
      font-weight: bold;
      color: var(--text-light);
    }
    
    .bp-width {
      color: var(--text-secondary);
      font-size: var(--font-sm);
    }
    
    .bp-status {
      color: var(--primary-color);
      font-weight: bold;
    }
  }
}

.spacing-demo {
  background: var(--primary-color-10);
  border: 1px solid var(--primary-color);
  border-radius: var(--radius-sm);
  color: var(--primary-color);
  font-size: var(--font-sm);
}

.test-preview {
  .preview-grid {
    .responsive-grid(2, var(--spacing-lg));
  }
  
  .preview-item {
    background: var(--card-bg);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    
    h4 {
      color: var(--primary-color);
      margin-bottom: var(--spacing-md);
      text-align: center;
    }
  }
}

.mini-cards-grid {
  .responsive-grid(5, var(--spacing-sm));
  
  .respond-to(xs) {
    .content() {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  .respond-to(sm) {
    .content() {
      grid-template-columns: repeat(3, 1fr);
    }
  }
}

.mini-card {
  background: var(--bg-dark-40);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  
  .mini-icon {
    width: 20px;
    height: 20px;
    background: var(--primary-color);
    border-radius: 50%;
  }
  
  .mini-info {
    flex: 1;
    min-width: 0;
    
    .mini-label {
      font-size: 10px;
      color: var(--text-secondary);
    }
    
    .mini-value {
      font-size: 12px;
      color: var(--primary-color);
      font-weight: bold;
    }
  }
}

.mini-kpi-grid {
  .responsive-grid(3, var(--spacing-sm));
  
  .respond-to(xs) {
    .content() {
      grid-template-columns: 1fr;
    }
  }
}

.mini-kpi-card {
  background: var(--bg-dark-40);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  
  .mini-kpi-icon {
    width: 32px;
    height: 32px;
    background: var(--primary-color-20);
    border: 1px solid var(--primary-color);
    border-radius: 50%;
  }
  
  .mini-kpi-info {
    flex: 1;
    
    .mini-kpi-label {
      font-size: 12px;
      color: var(--text-secondary);
      margin-bottom: 2px;
    }
    
    .mini-kpi-value {
      font-size: 14px;
      color: var(--primary-color);
      font-weight: bold;
    }
  }
}
</style>
