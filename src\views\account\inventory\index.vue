<template>
  <div class="p-4">
    <BasicTable @register="registerTable">
      <!-- 金额格式化 -->
      <template #amount="{ text }">
        <span class="font-medium">{{ formatAmount(text) }}</span>
      </template>

      <!-- 状态列 -->
      <template #status="{ record }">
        <a-tag :color="getStatusColor(record.status)">
          {{ getStatusText(record.status) }}
        </a-tag>
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" setup name="AccountInventory">
  import { ref } from 'vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import { BasicColumn } from '/@/components/Table';
  import { formatToDateTime } from '/@/utils/dateUtil';

  // 表格列定义
  const columns: BasicColumn[] = [
    {
      title: '交易单号',
      dataIndex: 'transactionId',
      width: 180,
    },
    {
      title: '类型',
      dataIndex: 'type',
      width: 80,
      slots: { customRender: 'type' },
    },
    {
      title: '项目',
      dataIndex: 'project',
      width: 200,
      ellipsis: true,
    },
    {
      title: '支付方式',
      dataIndex: 'paymentMethod',
      width: 120,
    },
    {
      title: '金额',
      dataIndex: 'amount',
      width: 120,
      slots: { customRender: 'amount' },
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      slots: { customRender: 'status' },
    },
    {
      title: '时间',
      dataIndex: 'createTime',
      width: 160,
      customRender: ({ text }) => formatToDateTime(text),
    },
  ];

  // 格式化金额
  function formatAmount(amount: number): string {
    return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  }

  // 获取状态颜色
  function getStatusColor(status: string): string {
    const colorMap: Record<string, string> = {
      success: 'green',
      pending: 'orange',
      failed: 'red',
    };
    return colorMap[status] || 'default';
  }

  // 获取状态文本
  function getStatusText(status: string): string {
    const textMap: Record<string, string> = {
      success: '成功',
      pending: '处理中',
      failed: '失败',
    };
    return textMap[status] || '未知';
  }

  // 模拟API调用
  async function queryInventoryList(params: any) {
    // 模拟假数据
    const mockData = [
      {
        id: 1,
        transactionId: 'TXN202412090001',
        type: 'XX特种钢',
        project: '资产处置收入',
        paymentMethod: '银行转账',
        amount: 50000.0,
        status: 'success',
        createTime: '2024-12-09 10:30:00',
      },
      {
        id: 2,
        transactionId: 'TXN202412090002',
        type: 'XX特种钢',
        project: '委托服务费',
        paymentMethod: '在线支付',
        amount: 2500.0,
        status: 'success',
        createTime: '2024-12-09 09:15:00',
      },
      {
        id: 3,
        transactionId: 'TXN202412080001',
        type: 'XX特种钢',
        project: '竞价保证金',
        paymentMethod: '银行转账',
        amount: 10000.0,
        status: 'pending',
        createTime: '2024-12-08 16:45:00',
      },
      {
        id: 4,
        transactionId: 'TXN202412080002',
        type: 'XX特种钢',
        project: '平台服务费',
        paymentMethod: '余额支付',
        amount: 1200.0,
        status: 'success',
        createTime: '2024-12-08 14:20:00',
      },
      {
        id: 5,
        transactionId: 'TXN202412070001',
        type: 'XX特种钢',
        project: '资产评估收入',
        paymentMethod: '银行转账',
        amount: 8000.0,
        status: 'failed',
        createTime: '2024-12-07 11:30:00',
      },
    ];

    // 模拟分页
    const { current = 1, size = 10 } = params;
    const start = (current - 1) * size;
    const end = start + size;
    const records = mockData.slice(start, end);

    return Promise.resolve({
      records,
      total: mockData.length,
      current,
      size,
    });
  }

  // 快捷时间选项
  const quickTimeOptions = [
    { label: '今天', value: 'today' },
    { label: '近7日', value: 'week' },
    { label: '近1月', value: 'month' },
  ];

  // 处理快捷时间选择
  function handleQuickTime(value: string) {
    const now = new Date();
    let startTime: Date;
    let endTime = new Date(now);

    switch (value) {
      case 'today':
        startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case 'week':
        startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startTime = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
        break;
      default:
        return;
    }

    // 设置表单时间区间值
    const form = getForm();
    form.setFieldsValue({
      timeRange: [startTime, endTime],
    });

    // 重新加载数据
    reload();
  }

  // 表格配置
  const [registerTable, { reload, getForm }] = useTable({
    api: queryInventoryList,
    columns,
    striped: false,
    useSearchForm: true,
    showTableSetting: false,
    bordered: false,
    showIndexColumn: false,
    canResize: true,
    inset: true,
    formConfig: {
      labelWidth: 80,
      size: 'large',
      schemas: [
        {
          field: 'quickTime',
          label: '',
          component: 'RadioGroup',
          componentProps: {
            options: quickTimeOptions,
            onChange: (e: any) => handleQuickTime(e.target.value),
            optionType: 'button',
            buttonStyle: 'solid',
            size: 'small',
          },
          colProps: { span: 6 },
        },
        {
          field: 'timeRange',
          label: '时间区间',
          component: 'RangePicker',
          componentProps: {
            showTime: true,
            format: 'YYYY-MM-DD HH:mm:ss',
          },
          colProps: { span: 12 },
        },
      ],
    },
  });
</script>

<style lang="less" scoped>
  .font-medium {
    font-weight: 500;
  }

  // 快捷时间按钮样式
  :deep(.ant-radio-group) {
    background: #f2f2f2;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 3px;
    white-space: nowrap;
    display: inline-flex;

    .ant-radio-button-wrapper {
      height: 36px !important;
      line-height: 36px !important;
      padding: 0 20px !important;
      font-size: 14px !important;
      background: transparent !important;
      color: #999 !important;
      border: none !important;
      border-left: none !important;
      margin-right: 0 !important;
      border-radius: 4px !important;

      &:first-child {
        border-radius: 4px !important;
        border-left: none !important;
      }

      &:last-child {
        border-radius: 4px !important;
      }

      &:hover {
        background: transparent !important;
        color: #999 !important;
      }

      &.ant-radio-button-wrapper-checked {
        background: #fff !important;
        color: #333 !important;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
        border: none !important;
        border-left: none !important;
      }

      &.ant-radio-button-wrapper-checked:hover {
        background: #fff !important;
        color: #333 !important;
      }

      // 强制去掉所有边框
      &::before {
        display: none !important;
      }
    }
  }
  :deep(.ant-btn-primary) {
    box-shadow: none;
  }
</style>
