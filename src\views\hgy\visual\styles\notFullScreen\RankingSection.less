.ranking-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
}

.ranking-container {
  position: relative;
  flex: 1;
  border-radius: 8px;
  overflow: hidden;
  min-height: 250px;
}

.ranking-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/right-bg_1754987518249.png') no-repeat center center;
  background-size: cover;
  z-index: 1;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(18, 230, 219, 0.05) 0%, rgba(18, 230, 219, 0.02) 50%, rgba(18, 230, 219, 0.05) 100%);
    border: 1px solid rgba(18, 230, 219, 0.2);
    border-radius: 8px;
  }
}

.ranking-content {
  position: relative;
  height: 100%;
  padding: 20px;
  z-index: 2;
}

.ranking-title {
  color: #12e6db;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  text-align: center;
  text-shadow: 0 0 10px rgba(18, 230, 219, 0.5);
}

.ranking-list {
  height: calc(100% - 50px);
  overflow: hidden;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;

  &:hover {
    background: rgba(18, 230, 219, 0.1);
  }

  &.top-three {
    .rank-number {
      background: linear-gradient(135deg, #12e6db 0%, #097884 100%);
      color: #004c66;
      font-weight: bold;
    }
  }
}

.rank-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: #004c66;
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
}

.rank-content {
  flex: 1;
  min-width: 0;
}

.rank-name {
  color: #ffffff;
  font-size: 14px;
  font-weight: 400;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2px;
}

.rank-value {
  color: #12e6db;
  font-size: 16px;
  font-weight: bold;
  text-shadow: 0 0 8px rgba(18, 230, 219, 0.5);
}

.rank-icon {
  width: 16px;
  height: 16px;
  margin-left: 8px;
  flex-shrink: 0;
}

.icon-triangle {
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 12px solid #12e6db;
  filter: drop-shadow(0 0 4px rgba(18, 230, 219, 0.6));

  &.rank-1 {
    border-bottom-color: #ffd700;
    filter: drop-shadow(0 0 4px rgba(255, 215, 0, 0.6));
  }

  &.rank-2 {
    border-bottom-color: #c0c0c0;
    filter: drop-shadow(0 0 4px rgba(192, 192, 192, 0.6));
  }

  &.rank-3 {
    border-bottom-color: #cd7f32;
    filter: drop-shadow(0 0 4px rgba(205, 127, 50, 0.6));
  }
}
