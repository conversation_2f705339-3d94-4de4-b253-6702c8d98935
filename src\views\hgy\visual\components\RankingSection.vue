<template>
  <div class="ranking-section">
    <!-- 标的溢价额排名 -->
    <div class="ranking-container">
      <div class="ranking-bg"></div>
      <div class="ranking-content">
        <div class="ranking-title">标的溢价额排名</div>
        <div class="ranking-list" ref="premiumAmountListRef">
          <div v-for="(item, index) in premiumAmountData" :key="index" class="ranking-item" :class="{ 'top-three': index < 3 }">
            <div class="rank-number">{{ index + 1 }}</div>
            <div class="rank-content">
              <div class="rank-name">{{ item.name }}</div>
              <div class="rank-value">{{ item.value }}万</div>
            </div>
            <div class="rank-icon">
              <div class="icon-triangle" :class="`rank-${index + 1}`"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 标的溢价率排名 -->
    <div class="ranking-container">
      <div class="ranking-bg"></div>
      <div class="ranking-content">
        <div class="ranking-title">标的溢价率排名</div>
        <div class="ranking-list" ref="premiumRateListRef">
          <div v-for="(item, index) in premiumRateData" :key="index" class="ranking-item" :class="{ 'top-three': index < 3 }">
            <div class="rank-number">{{ index + 1 }}</div>
            <div class="rank-content">
              <div class="rank-name">{{ item.name }}</div>
              <div class="rank-value">{{ item.value }}%</div>
            </div>
            <div class="rank-icon">
              <div class="icon-triangle" :class="`rank-${index + 1}`"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 资产处置溢价率排名 -->
    <div class="ranking-container">
      <div class="ranking-bg"></div>
      <div class="ranking-content">
        <div class="ranking-title">资产处置溢价率排名</div>
        <div class="ranking-list" ref="assetDisposalListRef">
          <div v-for="(item, index) in assetDisposalData" :key="index" class="ranking-item" :class="{ 'top-three': index < 3 }">
            <div class="rank-number">{{ index + 1 }}</div>
            <div class="rank-content">
              <div class="rank-name">{{ item.name }}</div>
              <div class="rank-value">{{ item.value }}%</div>
            </div>
            <div class="rank-icon">
              <div class="icon-triangle" :class="`rank-${index + 1}`"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted } from 'vue';

  // 排名数据接口
  interface RankingItem {
    name: string;
    value: number;
  }

  const premiumAmountListRef = ref<HTMLElement>();
  const premiumRateListRef = ref<HTMLElement>();
  const assetDisposalListRef = ref<HTMLElement>();

  // 模拟数据
  const premiumAmountData = ref<RankingItem[]>([
    { name: '标的某某某项目A', value: 5969 },
    { name: '标的某某某项目B', value: 4569 },
    { name: '标的某某某项目C', value: 3969 },
    { name: '标的某某某项目D', value: 2969 },
    { name: '标的某某某项目E', value: 1969 },
    { name: '标的某某某项目F', value: 969 },
    { name: '标的某某某项目G', value: 869 },
    { name: '标的某某某项目H', value: 769 },
    { name: '标的某某某项目I', value: 669 },
    { name: '标的某某某项目J', value: 569 },
  ]);

  const premiumRateData = ref<RankingItem[]>([
    { name: '标的某某某项目A', value: 95.5 },
    { name: '标的某某某项目B', value: 92.3 },
    { name: '标的某某某项目C', value: 89.7 },
    { name: '标的某某某项目D', value: 87.2 },
    { name: '标的某某某项目E', value: 85.8 },
    { name: '标的某某某项目F', value: 83.4 },
    { name: '标的某某某项目G', value: 81.9 },
    { name: '标的某某某项目H', value: 79.6 },
    { name: '标的某某某项目I', value: 77.3 },
    { name: '标的某某某项目J', value: 75.1 },
  ]);

  const assetDisposalData = ref<RankingItem[]>([
    { name: '资产处置项目A', value: 88.9 },
    { name: '资产处置项目B', value: 86.5 },
    { name: '资产处置项目C', value: 84.2 },
    { name: '资产处置项目D', value: 82.7 },
    { name: '资产处置项目E', value: 80.3 },
    { name: '资产处置项目F', value: 78.9 },
    { name: '资产处置项目G', value: 76.4 },
    { name: '资产处置项目H', value: 74.8 },
    { name: '资产处置项目I', value: 72.5 },
    { name: '资产处置项目J', value: 70.2 },
  ]);

  // 滚动动画
  let scrollIntervals: NodeJS.Timeout[] = [];

  const startScrollAnimation = (element: HTMLElement) => {
    if (!element) return;

    const scrollHeight = element.scrollHeight;
    const clientHeight = element.clientHeight;

    if (scrollHeight <= clientHeight) return;

    let scrollTop = 0;
    const scrollStep = 1;
    const scrollDelay = 50;

    const interval = setInterval(() => {
      scrollTop += scrollStep;
      element.scrollTop = scrollTop;

      if (scrollTop >= scrollHeight - clientHeight) {
        // 滚动到底部后，暂停一下再重新开始
        setTimeout(() => {
          scrollTop = 0;
          element.scrollTop = 0;
        }, 2000);
      }
    }, scrollDelay);

    scrollIntervals.push(interval);
  };

  onMounted(() => {
    // 延迟启动滚动动画
    setTimeout(() => {
      if (premiumAmountListRef.value) {
        startScrollAnimation(premiumAmountListRef.value);
      }
      if (premiumRateListRef.value) {
        startScrollAnimation(premiumRateListRef.value);
      }
      if (assetDisposalListRef.value) {
        startScrollAnimation(assetDisposalListRef.value);
      }
    }, 3000);
  });

  onUnmounted(() => {
    scrollIntervals.forEach((interval) => clearInterval(interval));
    scrollIntervals = [];
  });
</script>

<style lang="less" scoped>
  @import '../styles/fullScreen/RankingSection.less';
</style>
