import { defHttp } from '/@/utils/http/axios';

// 个人认证数据结构
export interface PersonalAuthData {
  userId?: string; // 用户ID
  cartId: string; // 证件号
  name: string; // 真实姓名
  review: number; // 审核状态
  phone: string; // 手机号
  cartType: number; // 证件类型(1.身份证 2.其他)
  attachmentList: AttachmentItem[]; // 证件照片信息
}

// 企业认证数据结构
export interface EnterpriseAuthData {
  userId?: string; // 用户ID
  review: number; // 审核状态
  legalName: string; // 法人姓名
  enterpriseName: string; // 企业名称
  creditCode: string; // 信用代码
  relationUser: string; // 联系人
  relationPhone: string; // 联系电话
  cartType: number; // 法人证件类型(1.身份证 2.其他)
  cartId: string; // 法人证件号
  attachmentList: AttachmentItem[]; // 身份证正反面照片信息
  description?: string; // 企业简介
  companyLogo: string; // 营业执照照片
}

// 附件信息
export interface AttachmentItem {
  bizType: string;
  fileName: string;
  filePath: string;
  fileSize: string | number;
  fileType: string;
  bizId?: string;
  id?: string;
  userId?: string;
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  delFlag?: number;
  tenantId?: number;
}

// API响应的企业认证数据结构
export interface EnterpriseAuthResponse {
  hgyEnterpriseAuth: EnterpriseAuthData & {
    notes?: string;
    companyLogo?: string;
    updateTime?: string | null;
    delFlag?: number;
    defaultActivate?: number;
    createBy?: string;
    review_dictText?: string;
    createTime?: string;
    updateBy?: string | null;
    tenantId?: number;
    reviewUser?: string;
    id?: string | null;
    reviewTime?: number;
  };
  hgyAttachmentList: AttachmentItem[];
}

// API响应的个人认证数据结构
export interface PersonalAuthResponse {
  hgyPersonalAuth: PersonalAuthData & {
    notes?: string;
    updateTime?: string | null;
    delFlag?: number;
    createBy?: string;
    review_dictText?: string;
    createTime?: string;
    updateBy?: string | null;
    tenantId?: number;
    reviewUser?: string;
    id?: string | null;
    reviewTime?: number;
  };
  hgyAttachmentList: AttachmentItem[];
}

// API接口
enum Api {
  PersonalAuth = '/hgy/personalCenter/hgyPersonalAuth/addOrUpdatePersonalAuth',
  EnterpriseAuth = '/hgy/personalCenter/hgyEnterpriseAuth/addOrUpdateEnterpriseAuth',
  GetPersonalAuth = '/hgy/personalCenter/hgyPersonalAuth/getPersonalAuthByUserId',
  GetEnterpriseAuth = '/hgy/personalCenter/hgyEnterpriseAuth/getEnterpriseAuthByUserId',
}

/**
 * 提交个人认证
 */
export const submitPersonalAuth = (params: PersonalAuthData) => {
  return defHttp.post<any>({
    url: Api.PersonalAuth,
    params,
  });
};

/**
 * 提交企业认证
 */
export const submitEnterpriseAuth = (params: EnterpriseAuthData) => {
  return defHttp.post<any>({
    url: Api.EnterpriseAuth,
    params,
  });
};

/**
 * 获取个人认证信息
 */
export const getPersonalAuth = (userId: string | number) => {
  return defHttp.get<PersonalAuthResponse>({
    url: Api.GetPersonalAuth,
    params: { userId },
  });
};

/**
 * 获取企业认证信息
 */
export const getEnterpriseAuth = (userId: string | number) => {
  return defHttp.get<EnterpriseAuthResponse>({
    url: Api.GetEnterpriseAuth,
    params: { userId },
  });
};
