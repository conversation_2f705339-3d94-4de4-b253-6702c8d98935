<template>
  <div class="p-4">
    <BasicTable @register="registerTable">
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              label: '重发',
              icon: 'ant-design:redo-outlined',
              onClick: handleResend.bind(null, record),
              ifShow: () => record.sendStatus === 'failed',
            },
            {
              label: '删除',
              icon: 'ant-design:delete-outlined',
              color: 'error',
              popConfirm: {
                title: '确定要删除这条留言吗？',
                confirm: handleDelete.bind(null, record),
              },
            },
          ]"
        />
      </template>
      <template #sendStatus="{ record }">
        <a-tag :color="getStatusColor(record.sendStatus)">
          {{ getStatusText(record.sendStatus) }}
        </a-tag>
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { BasicColumn } from '/@/components/Table';
  import { FormSchema } from '/@/components/Form';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useUserStore } from '/@/store/modules/user';
  import { Tag as ATag } from 'ant-design-vue';
  import { queryAllSend } from './outbox.api';

  const { createMessage } = useMessage();
  const userStore = useUserStore();

  // 表格列配置
  const columns: BasicColumn[] = [
    {
      title: '接收者',
      dataIndex: 'receiver',
      width: 120,
      resizable: true,
    },
    {
      title: '关联产品信息',
      dataIndex: 'productInfo',
      width: 200,
      resizable: true,
    },
    {
      title: '留言内容',
      dataIndex: 'content',
      width: 300,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '发送状态',
      dataIndex: 'sendStatus',
      width: 120,
      resizable: true,
      slots: { customRender: 'sendStatus' },
    },
    {
      title: '发送时间',
      dataIndex: 'sendTime',
      width: 180,
      resizable: true,
    },
  ];

  // 搜索表单配置
  const searchFormSchema: FormSchema[] = [
    {
      field: 'keywords',
      label: '关键词',
      component: 'Input',
      componentProps: {
        placeholder: '请输入关键词搜索',
      },
      colProps: { span: 6 },
    },
    {
      field: 'sendStatus',
      label: '发送状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择发送状态',
        options: [
          { label: '发送成功', value: 'success' },
          { label: '发送失败', value: 'failed' },
          { label: '发送中', value: 'sending' },
        ],
      },
      colProps: { span: 6 },
    },
    {
      field: 'messageStatus',
      label: '消息状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择消息状态',
        options: [
          { label: '未读', value: 'unread' },
          { label: '已读', value: 'read' },
          { label: '已回复', value: 'replied' },
        ],
      },
      colProps: { span: 6 },
    },
    {
      field: 'timeRange',
      label: '时间区间',
      component: 'RangePicker',
      componentProps: {
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
        placeholder: ['开始时间', '结束时间'],
      },
      colProps: { span: 6 },
    },
  ];

  // 获取状态颜色
  function getStatusColor(status: string) {
    const colorMap = {
      success: 'green',
      failed: 'red',
      sending: 'blue',
    };
    return colorMap[status] || 'default';
  }

  // 获取状态文本
  function getStatusText(status: string) {
    const textMap = {
      success: '发送成功',
      failed: '发送失败',
      sending: '发送中',
    };
    return textMap[status] || status;
  }

  // API请求函数
  const fetchData = async (params: any) => {
    console.log('查询参数:', params);

    // 构建请求参数
    const requestParams = {
      pageNo: params.page || 1,
      pageSize: params.pageSize || 10,
      sendUserId: String(userStore.getUserInfo?.id || ''),
      keywords: params.keywords || '发',
    };

    try {
      const result = await queryAllSend(requestParams);

      return {
        items: result.records || [],
        total: result.total || 0,
      };
    } catch (error) {
      console.error('查询发件箱失败:', error);
      return {
        items: [],
        total: 0,
      };
    }
  };

  // 表格配置
  const [registerTable, { reload }] = useTable({
    api: fetchData,
    columns,
    striped: false,
    useSearchForm: true,
    showTableSetting: false,
    bordered: false,
    showIndexColumn: false,
    canResize: true,
    inset: true,
    maxHeight: 478,
    actionColumn: {
      width: 160,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      fixed: 'right',
    },
    rowKey: 'id',
    formConfig: {
      labelWidth: 64,
      size: 'large',
      schemas: searchFormSchema,
      actionColOptions: {
        span: 6,
        style: {
          textAlign: 'left',
        },
      },
    },
  });

  // 重发留言
  function handleResend(record: any) {
    console.log('重发留言:', record);
    createMessage.info(`正在重发给 ${record.receiver} 的留言...`);
    // 模拟重发操作
    setTimeout(() => {
      createMessage.success('留言重发成功');
      reload();
    }, 1000);
  }

  // 删除留言
  function handleDelete(record: any) {
    console.log('删除留言:', record);
    createMessage.success(`已删除发给 ${record.receiver} 的留言`);
    reload();
  }
</script>

<style lang="less" scoped>
  .p-4 {
    padding: 0;
    :deep(.ant-pagination) {
      margin-bottom: -24px !important;
    }
    :deep(.ant-form) {
      padding: 0;
    }
  }

  :deep(.ant-form-item-control-input-content) {
    button {
      margin-right: 0;
      margin-left: 8px;
      box-shadow: 0 0 0 rgba(3, 38, 43, 0.42);
    }
  }
</style>
