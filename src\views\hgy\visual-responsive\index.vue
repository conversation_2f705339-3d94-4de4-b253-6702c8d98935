<!--
  全新响应式数据可视化大屏主页面
  
  功能特性：
  1. 现代响应式布局，使用CSS Grid和Flexbox
  2. 基于CSS变量的主题系统
  3. 移动端优先的设计理念
  4. 流体布局，自适应各种屏幕尺寸
  5. 保持原有视觉效果和布局结构
  
  布局结构：
  - 头部：标题、时间、导航信息
  - 主体：三栏响应式布局（左侧排名、中间指标、右侧排名）
  - 底部：三个图表卡片响应式排列
-->
<template>
  <div class="responsive-visual-container" :class="{ fullscreen: isFullscreen }">
    <!-- 星空背景效果 -->
    <div class="visual-bg"></div>
    
    <!-- 主要内容区域 - 响应式网格布局 -->
    <div class="responsive-visual-content">
      <!-- 头部区域 -->
      <header class="visual-header">
        <ResponsiveHeader 
          @material-change="handleMaterialChange" 
          @area-change="handleAreaChange" 
        />
      </header>
      
      <!-- 主体内容区域 - 三栏响应式布局 -->
      <main class="visual-main">
        <!-- 左侧：标的数据排名 -->
        <aside class="visual-left">
          <ResponsiveTargetRanking />
        </aside>
        
        <!-- 中间：数据指标和小卡片 -->
        <section class="visual-center">
          <div class="center-content">
            <!-- 上部：主要KPI指标 -->
            <div class="center-top">
              <ResponsiveMainKPI />
            </div>
            <!-- 下部：小卡片网格 -->
            <div class="center-bottom">
              <ResponsiveSmallCards />
            </div>
          </div>
        </section>
        
        <!-- 右侧：资产处置数据排名 -->
        <aside class="visual-right">
          <ResponsiveAssetRanking />
        </aside>
      </main>
      
      <!-- 底部区域：图表卡片响应式网格 -->
      <footer class="visual-footer">
        <ResponsiveBottomCharts />
      </footer>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, provide } from 'vue';
import { useRouter } from 'vue-router';
import ResponsiveHeader from './components/ResponsiveHeader.vue';
import ResponsiveTargetRanking from './components/ResponsiveTargetRanking.vue';
import ResponsiveMainKPI from './components/ResponsiveMainKPI.vue';
import ResponsiveSmallCards from './components/ResponsiveSmallCards.vue';
import ResponsiveAssetRanking from './components/ResponsiveAssetRanking.vue';
import ResponsiveBottomCharts from './components/ResponsiveBottomCharts.vue';
import { useResponsiveLayout } from './composables/useResponsiveLayout';
import { useFullscreen } from './composables/useFullscreen';
import { dataService } from '../visual/services/dataService';

const router = useRouter();
const { isFullscreen, enterFullscreen, exitFullscreen } = useFullscreen();
const { updateLayout, screenSize, breakpoint } = useResponsiveLayout();

// 提供数据服务给子组件
provide('dataService', dataService);
provide('screenSize', screenSize);
provide('breakpoint', breakpoint);

// 处理物资类型变化
const handleMaterialChange = (value: string[], text: string) => {
  console.log('物资类型变化:', { value, text });
  // 这里可以根据选择的物资类型更新数据大屏的数据
};

// 处理省市区变化
const handleAreaChange = (value: string[], text: string) => {
  console.log('省市区变化:', { value, text });
  // 这里可以根据选择的省市区更新数据大屏的数据
};

// 键盘事件监听
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && isFullscreen.value) {
    exitFullscreen();
  } else if (event.key === 'F11') {
    event.preventDefault();
    if (isFullscreen.value) {
      exitFullscreen();
    } else {
      enterFullscreen();
    }
  }
};

// 全屏状态变化处理
const handleFullscreenChange = () => {
  const isCurrentlyFullscreen = !!(
    document.fullscreenElement ||
    (document as any).webkitFullscreenElement ||
    (document as any).msFullscreenElement
  );
  isFullscreen.value = isCurrentlyFullscreen;
  
  // 如果是通过URL参数进入的全屏模式，退出时返回首页
  const urlParams = new URLSearchParams(window.location.search);
  if (!isCurrentlyFullscreen && urlParams.get('fullscreen') === 'true') {
    router.push('/dashboard/analysis');
  }
};

onMounted(() => {
  // 检查是否需要自动进入全屏
  const urlParams = new URLSearchParams(window.location.search);
  if (urlParams.get('fullscreen') === 'true') {
    enterFullscreen();
  }
  
  // 添加事件监听
  document.addEventListener('fullscreenchange', handleFullscreenChange);
  document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
  document.addEventListener('msfullscreenchange', handleFullscreenChange);
  document.addEventListener('keydown', handleKeydown);
  window.addEventListener('resize', updateLayout);
  
  // 初始化布局
  updateLayout();
});

onUnmounted(() => {
  // 移除事件监听
  document.removeEventListener('fullscreenchange', handleFullscreenChange);
  document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
  document.removeEventListener('msfullscreenchange', handleFullscreenChange);
  document.removeEventListener('keydown', handleKeydown);
  window.removeEventListener('resize', updateLayout);
});
</script>

<style lang="less" scoped>
@import './styles/responsive-variables.less';
@import './styles/responsive-mixins.less';

.responsive-visual-container {
  position: relative;
  width: 100%;
  height: 100vh;
  min-height: 100vh;
  overflow: hidden;
  background: var(--bg-dark);
  
  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
  }
  
  // 星空背景效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
      radial-gradient(2px 2px at 20px 30px, var(--primary-color), transparent),
      radial-gradient(2px 2px at 40px 70px, var(--primary-color-80), transparent),
      radial-gradient(1px 1px at 90px 40px, var(--primary-color-60), transparent),
      radial-gradient(1px 1px at 130px 80px, var(--primary-color-40), transparent),
      radial-gradient(2px 2px at 160px 30px, var(--primary-color-70), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: twinkle 4s ease-in-out infinite alternate;
    z-index: 0;
  }
}

@keyframes twinkle {
  0% { opacity: 0.3; }
  100% { opacity: 0.8; }
}

.visual-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/visual-bg_1755478111715.png') no-repeat center center;
  background-size: cover;
  z-index: 1;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg, 
      var(--bg-dark-20) 0%, 
      transparent 30%, 
      transparent 70%, 
      var(--bg-dark-20) 100%
    );
  }
}

.responsive-visual-content {
  position: relative;
  width: 100%;
  height: 100vh;
  z-index: 2;
  display: grid;
  grid-template-rows: auto 1fr auto;
  grid-template-areas: 
    "header"
    "main"
    "footer";
  gap: var(--spacing-md);
  padding: var(--spacing-sm);
  box-sizing: border-box;
}

.visual-header {
  grid-area: header;
  height: var(--header-height);
  min-height: var(--header-height);
}

.visual-main {
  grid-area: main;
  display: grid;
  grid-template-columns: var(--sidebar-width) 1fr var(--sidebar-width);
  grid-template-areas: "left center right";
  gap: var(--spacing-md);
  min-height: 0; // 允许内容收缩
}

.visual-left {
  grid-area: left;
  min-width: 0; // 防止内容溢出
}

.visual-center {
  grid-area: center;
  min-width: 0;
  display: flex;
  flex-direction: column;
}

.center-content {
  flex: 1;
  display: grid;
  grid-template-rows: auto 1fr;
  gap: var(--spacing-md);
}

.center-top {
  display: flex;
  justify-content: center;
  align-items: center;
}

.center-bottom {
  display: flex;
  justify-content: center;
  align-items: center;
}

.visual-right {
  grid-area: right;
  min-width: 0;
}

.visual-footer {
  grid-area: footer;
  height: var(--footer-height);
  min-height: var(--footer-height);
}

// 响应式断点
@media (max-width: @screen-xl) {
  .responsive-visual-content {
    gap: var(--spacing-sm);
    padding: var(--spacing-xs);
  }
  
  .visual-main {
    gap: var(--spacing-sm);
  }
}

@media (max-width: @screen-lg) {
  .visual-main {
    grid-template-columns: 1fr;
    grid-template-areas: 
      "center"
      "left"
      "right";
    gap: var(--spacing-sm);
  }
}

@media (max-width: @screen-md) {
  .responsive-visual-content {
    grid-template-rows: auto 1fr;
    grid-template-areas: 
      "header"
      "main";
  }
  
  .visual-footer {
    display: none; // 在小屏幕上隐藏底部图表
  }
}
</style>
