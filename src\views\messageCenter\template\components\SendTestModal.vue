<template>
  <!-- 发送测试弹窗 -->
  <CustomModal
    v-model:open="visible"
    title="发送测试"
    width="600px"
    :show-footer="true"
    :show-cancel-button="true"
    :show-confirm-button="true"
    cancel-text="取消"
    confirm-text="发送"
    @confirm="handleSendTest"
    @cancel="handleCancel"
  >
    <div class="send-modal-content">
      <a-form ref="sendFormRef" :model="sendFormData" :rules="sendFormRules" layout="vertical">
        <a-form-item label="模板标题">
          <a-input :value="sendFormData.templateName" disabled />
        </a-form-item>

        <a-form-item label="模板内容">
          <a-textarea :value="sendFormData.templateContent" disabled :rows="5" />
        </a-form-item>

        <a-form-item label="测试数据" name="testData">
          <a-textarea
            v-model:value="sendFormData.testData"
            placeholder='请输入JSON格式测试数据，例如：{"name": "张三", "phone": "13800138000"}'
            :rows="5"
          />
        </a-form-item>

        <a-form-item label="消息接收方" name="receiver">
          <div class="receiver-select-container">
            <a-select
              v-model:value="selectedUserTags"
              mode="multiple"
              placeholder="请选择消息接收方"
              style="width: calc(100% - 80px)"
              :open="false"
              @click="handleSelectReceiver"
              @change="handleUserTagsChange"
            >
              <a-select-option v-for="user in currentSelectedUsers" :key="user.username" :value="user.username">
                {{ user.realname }}({{ user.username }})
              </a-select-option>
            </a-select>
            <a-button type="primary" @click="handleSelectReceiver" style="width: 80px; margin-left: 8px">选择</a-button>
          </div>
        </a-form-item>

        <a-form-item label="消息类型" name="msgType">
          <a-radio-group v-model:value="sendFormData.msgType">
            <a-radio value="system">系统消息</a-radio>
            <a-radio value="user">用户消息</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </div>

    <!-- 用户选择弹窗 -->
    <UserSelectModal v-model:open="userSelectModalVisible" :selected-users="currentSelectedUsers" @confirm="handleConfirmSelectUser" />
  </CustomModal>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, watch } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import CustomModal from '/@/components/Modal/src/CustomModal.vue';
  import { sendMessageTest } from '/@/views/system/message/template/template.api';
  import UserSelectModal from './UserSelectModal.vue';

  const { createMessage } = useMessage();

  interface Props {
    open: boolean;
    record?: any;
  }

  interface Emits {
    (e: 'update:open', value: boolean): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    open: false,
    record: () => ({}),
  });

  const emit = defineEmits<Emits>();

  const visible = computed({
    get: () => props.open,
    set: (value: boolean) => emit('update:open', value),
  });

  const sendFormRef = ref();
  const userSelectModalVisible = ref(false);
  const receiverDisplayText = ref('');
  const currentSelectedUsers = ref<any[]>([]);
  const selectedUserTags = ref<string[]>([]);

  // 发送表单数据
  const sendFormData = reactive({
    templateCode: '',
    templateName: '',
    templateContent: '',
    testData: '{}',
    msgType: 'system',
    receiver: '',
  });

  // 发送测试表单验证规则
  const sendFormRules = {
    testData: [{ required: true, message: '请输入测试数据', trigger: 'blur' }],
    msgType: [{ required: true, message: '请选择消息类型', trigger: 'change' }],
    receiver: [{ required: true, message: '请选择消息接收方', trigger: 'change' }],
  };

  // 监听record变化，更新表单数据
  watch(
    () => props.record,
    (newRecord) => {
      if (newRecord && typeof newRecord === 'object' && Object.keys(newRecord).length > 0) {
        Object.assign(sendFormData, {
          templateCode: newRecord.templateCode || '',
          templateName: newRecord.templateName || '',
          templateContent: newRecord.templateContent || '',
          testData: '{}',
          msgType: 'system',
          receiver: '',
        });
        // 重置接收方显示
        receiverDisplayText.value = '';
      }
    },
    { immediate: true }
  );

  // 选择消息接收方
  const handleSelectReceiver = () => {
    userSelectModalVisible.value = true;
  };

  // 处理用户标签变化（删除标签时）
  const handleUserTagsChange = (selectedUsernames: string[]) => {
    // 根据剩余的用户名过滤出对应的用户数据
    const remainingUsers = currentSelectedUsers.value.filter((user) => selectedUsernames.includes(user.username));

    // 更新选中用户数据
    currentSelectedUsers.value = remainingUsers;

    // 更新接收方数据
    sendFormData.receiver = selectedUsernames.join(',');

    // 更新显示文本
    const displayNames = remainingUsers.map((user) => `${user.realname}(${user.username})`);
    receiverDisplayText.value = displayNames.join(', ');
  };

  // 确认选择用户
  const handleConfirmSelectUser = (selectedUsers: any[]) => {
    if (selectedUsers.length === 0) {
      createMessage.warning('请选择至少一个用户');
      return;
    }

    // 保存当前选中的用户数据
    currentSelectedUsers.value = selectedUsers;

    // 设置接收方数据
    const usernames = selectedUsers.map((user) => user.username);
    sendFormData.receiver = usernames.join(',');
    selectedUserTags.value = usernames;

    // 设置显示文本（保留兼容性）
    const displayNames = selectedUsers.map((user) => `${user.realname}(${user.username})`);
    receiverDisplayText.value = displayNames.join(', ');

    userSelectModalVisible.value = false;
  };

  // 发送测试消息
  const handleSendTest = async () => {
    try {
      await sendFormRef.value?.validate();

      // 验证JSON格式
      try {
        JSON.parse(sendFormData.testData);
      } catch {
        createMessage.error('测试数据格式错误，请输入有效的JSON格式');
        return;
      }

      await sendMessageTest(sendFormData);
      createMessage.success('测试消息发送成功');
      visible.value = false;
    } catch (error) {
      console.error('发送失败:', error);
    }
  };

  // 取消发送测试
  const handleCancel = () => {
    visible.value = false;
  };
</script>

<style lang="less" scoped>
  .send-modal-content {
    padding: 16px 0;
  }

  .receiver-select-container {
    display: flex;
    align-items: center;
    width: 100%;
  }

  :deep(.ant-form-item-label) {
    font-weight: 500;
  }

  :deep(.ant-input-group) {
    display: flex;
    align-items: center;
  }

  // 多选下拉框样式优化
  :deep(.ant-select-multiple .ant-select-selection-item) {
    background-color: #f0f0f0;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    margin-right: 4px;
    padding: 2px 8px;
  }

  :deep(.ant-select-multiple .ant-select-selection-item-content) {
    color: #333;
    font-size: 12px;
  }
</style>
