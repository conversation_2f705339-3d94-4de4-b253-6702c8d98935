// 响应式数据可视化大屏 - CSS变量定义

// 颜色系统
:root {
  // 主色调
  --primary-color: #2bccff;
  --primary-color-80: rgba(43, 204, 255, 0.8);
  --primary-color-70: rgba(43, 204, 255, 0.7);
  --primary-color-60: rgba(43, 204, 255, 0.6);
  --primary-color-40: rgba(43, 204, 255, 0.4);
  --primary-color-20: rgba(43, 204, 255, 0.2);
  --primary-color-10: rgba(43, 204, 255, 0.1);
  --primary-dark: #1a8fcc;
  
  // 背景色
  --bg-dark: #0a0e27;
  --bg-dark-80: rgba(10, 14, 39, 0.8);
  --bg-dark-60: rgba(10, 14, 39, 0.6);
  --bg-dark-40: rgba(10, 14, 39, 0.4);
  --bg-dark-20: rgba(10, 14, 39, 0.2);
  
  // 文字颜色
  --text-light: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.8);
  --text-muted: rgba(255, 255, 255, 0.6);
  --text-disabled: rgba(255, 255, 255, 0.4);
  
  // 边框和发光效果
  --border-glow: rgba(43, 204, 255, 0.3);
  --border-light: rgba(255, 255, 255, 0.1);
  
  // 渐变色
  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  --gradient-bg: linear-gradient(135deg, var(--bg-dark) 0%, rgba(43, 204, 255, 0.05) 50%, var(--bg-dark) 100%);
  
  // 阴影
  --shadow-glow: 0 0 20px var(--primary-color-20);
  --shadow-card: 0 4px 20px rgba(0, 0, 0, 0.3);
  --shadow-text: 0 0 10px var(--primary-color-40);
}

// 布局尺寸 - 桌面端
:root {
  --header-height: 88px;
  --footer-height: 280px;
  --sidebar-width: minmax(300px, 1fr);
  
  // 间距系统
  --spacing-xs: 8px;
  --spacing-sm: 12px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  
  // 字体大小
  --font-xs: 12px;
  --font-sm: 14px;
  --font-md: 16px;
  --font-lg: 18px;
  --font-xl: 20px;
  --font-2xl: 24px;
  --font-3xl: 32px;
  
  // 圆角
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  
  // 动画时长
  --duration-fast: 0.15s;
  --duration-normal: 0.3s;
  --duration-slow: 0.5s;
  
  // Z-index层级
  --z-background: 0;
  --z-content: 1;
  --z-overlay: 10;
  --z-modal: 100;
  --z-tooltip: 1000;
}

// 平板端适配
@media (max-width: 1200px) {
  :root {
    --header-height: 80px;
    --footer-height: 240px;
    --sidebar-width: minmax(280px, 1fr);
    
    --spacing-xs: 6px;
    --spacing-sm: 10px;
    --spacing-md: 14px;
    --spacing-lg: 20px;
    --spacing-xl: 28px;
    --spacing-2xl: 40px;
    
    --font-xs: 11px;
    --font-sm: 13px;
    --font-md: 15px;
    --font-lg: 17px;
    --font-xl: 19px;
    --font-2xl: 22px;
    --font-3xl: 28px;
  }
}

// 移动端适配
@media (max-width: 768px) {
  :root {
    --header-height: 60px;
    --footer-height: 200px;
    --sidebar-width: 1fr;
    
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --spacing-xl: 24px;
    --spacing-2xl: 32px;
    
    --font-xs: 10px;
    --font-sm: 12px;
    --font-md: 14px;
    --font-lg: 16px;
    --font-xl: 18px;
    --font-2xl: 20px;
    --font-3xl: 24px;
  }
}

// 小屏移动端适配
@media (max-width: 480px) {
  :root {
    --header-height: 50px;
    --footer-height: 160px;
    
    --spacing-xs: 4px;
    --spacing-sm: 6px;
    --spacing-md: 10px;
    --spacing-lg: 14px;
    --spacing-xl: 20px;
    --spacing-2xl: 28px;
    
    --font-xs: 9px;
    --font-sm: 11px;
    --font-md: 13px;
    --font-lg: 15px;
    --font-xl: 17px;
    --font-2xl: 19px;
    --font-3xl: 22px;
  }
}

// 断点变量 (与原项目保持一致)
@screen-xs: 480px;
@screen-sm: 576px;
@screen-md: 768px;
@screen-lg: 992px;
@screen-xl: 1200px;
@screen-2xl: 1600px;

// 容器最大宽度
@container-xs: 100%;
@container-sm: 540px;
@container-md: 720px;
@container-lg: 960px;
@container-xl: 1140px;
@container-2xl: 1320px;

// 网格系统
@grid-columns: 12;
@grid-gutter-width: 30px;

// 组件特定变量
:root {
  // 卡片
  --card-bg: var(--bg-dark-80);
  --card-border: var(--border-light);
  --card-radius: var(--radius-lg);
  --card-padding: var(--spacing-lg);
  
  // 按钮
  --button-height: 40px;
  --button-padding: var(--spacing-sm) var(--spacing-lg);
  --button-radius: var(--radius-md);
  
  // 输入框
  --input-height: 36px;
  --input-padding: var(--spacing-sm);
  --input-radius: var(--radius-sm);
  
  // 图表
  --chart-height: 300px;
  --chart-padding: var(--spacing-md);
  
  // 排名列表
  --rank-item-height: 48px;
  --rank-item-padding: var(--spacing-sm);
}

// 移动端组件适配
@media (max-width: 768px) {
  :root {
    --card-padding: var(--spacing-md);
    --button-height: 36px;
    --input-height: 32px;
    --chart-height: 240px;
    --rank-item-height: 40px;
  }
}
