<template>
  <span class="count-to">{{ displayValue }}</span>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';

interface Props {
  startVal?: number;
  endVal: number;
  duration?: number;
  autoplay?: boolean;
  decimals?: number;
  decimal?: string;
  separator?: string;
  prefix?: string;
  suffix?: string;
  useEasing?: boolean;
  easingFn?: (t: number, b: number, c: number, d: number) => number;
}

const props = withDefaults(defineProps<Props>(), {
  startVal: 0,
  duration: 3000,
  autoplay: true,
  decimals: 0,
  decimal: '.',
  separator: ',',
  prefix: '',
  suffix: '',
  useEasing: true,
  easingFn: (t: number, b: number, c: number, d: number) => {
    // easeOutExpo
    return c * (-Math.pow(2, -10 * t / d) + 1) * 1024 / 1023 + b;
  },
});

const localStartVal = ref(props.startVal);
const displayValue = ref(formatNumber(props.startVal));
const printVal = ref<number | null>(null);
const paused = ref(false);
const localDuration = ref(props.duration);
const startTime = ref<number | null>(null);
const timestamp = ref<number | null>(null);
const remaining = ref<number | null>(null);
const rAF = ref<number | null>(null);

const countDown = computed(() => props.startVal > props.endVal);

watch(() => props.startVal, () => {
  if (props.autoplay) {
    start();
  }
});

watch(() => props.endVal, () => {
  if (props.autoplay) {
    start();
  }
});

// 格式化数字
function formatNumber(num: number): string {
  const { decimals, decimal, separator, suffix, prefix } = props;
  
  num = Number(num).toFixed(decimals);
  num += '';
  
  const x = num.split('.');
  let x1 = x[0];
  const x2 = x.length > 1 ? decimal + x[1] : '';
  
  const rgx = /(\d+)(\d{3})/;
  if (separator && !isNaN(Number(x1))) {
    while (rgx.test(x1)) {
      x1 = x1.replace(rgx, '$1' + separator + '$2');
    }
  }
  
  return prefix + x1 + x2 + suffix;
}

// 缓动函数
function easingFn(t: number, b: number, c: number, d: number): number {
  return props.easingFn(t, b, c, d);
}

// 计数函数
function count(timestamp: number) {
  if (!startTime.value) startTime.value = timestamp;
  
  const progress = timestamp - startTime.value;
  remaining.value = localDuration.value - progress;
  
  if (props.useEasing) {
    if (countDown.value) {
      printVal.value = localStartVal.value - easingFn(progress, 0, localStartVal.value - props.endVal, localDuration.value);
    } else {
      printVal.value = easingFn(progress, localStartVal.value, props.endVal - localStartVal.value, localDuration.value);
    }
  } else {
    if (countDown.value) {
      printVal.value = localStartVal.value - ((localStartVal.value - props.endVal) * (progress / localDuration.value));
    } else {
      printVal.value = localStartVal.value + (props.endVal - localStartVal.value) * (progress / localDuration.value);
    }
  }
  
  if (countDown.value) {
    printVal.value = printVal.value < props.endVal ? props.endVal : printVal.value;
  } else {
    printVal.value = printVal.value > props.endVal ? props.endVal : printVal.value;
  }
  
  displayValue.value = formatNumber(printVal.value);
  
  if (progress < localDuration.value) {
    rAF.value = requestAnimationFrame(count);
  } else {
    displayValue.value = formatNumber(props.endVal);
  }
}

// 开始计数
function start() {
  localStartVal.value = props.startVal;
  startTime.value = null;
  localDuration.value = props.duration;
  paused.value = false;
  rAF.value = requestAnimationFrame(count);
}

// 暂停计数
function pauseResume() {
  if (paused.value) {
    resume();
    paused.value = false;
  } else {
    pause();
    paused.value = true;
  }
}

// 暂停
function pause() {
  if (rAF.value) {
    cancelAnimationFrame(rAF.value);
  }
}

// 恢复
function resume() {
  startTime.value = null;
  localDuration.value = remaining.value || 0;
  localStartVal.value = printVal.value || 0;
  requestAnimationFrame(count);
}

// 重置
function reset() {
  startTime.value = null;
  if (rAF.value) {
    cancelAnimationFrame(rAF.value);
  }
  displayValue.value = formatNumber(props.startVal);
}

onMounted(() => {
  if (props.autoplay) {
    start();
  }
  displayValue.value = formatNumber(props.startVal);
});

onUnmounted(() => {
  if (rAF.value) {
    cancelAnimationFrame(rAF.value);
  }
});

// 暴露方法给父组件
defineExpose({
  start,
  pause,
  resume,
  reset,
  pauseResume,
});
</script>

<style lang="less" scoped>
.count-to {
  display: inline-block;
  font-variant-numeric: tabular-nums;
  font-feature-settings: 'tnum';
}
</style>
