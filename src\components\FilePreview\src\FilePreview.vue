<template>
  <CustomModal
    v-model:open="visible"
    :title="modalTitle"
    :width="modalWidth"
    :show-footer="false"
    :mask-closable="true"
    :destroy-on-close="true"
    @close="handleClose"
  >
    <div class="file-preview-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <a-spin size="large" tip="文件加载中..." />
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <a-result status="error" :title="error" :sub-title="errorSubTitle">
          <template #extra>
            <a-button type="primary" @click="handleRetry">重试</a-button>
            <a-button @click="handleDownload">下载文件</a-button>
          </template>
        </a-result>
      </div>

      <!-- 预览内容 -->
      <div v-else class="preview-content">
        <!-- 图片预览 -->
        <div v-if="fileType === 'image'" class="image-preview">
          <img :src="fileUrl" :alt="fileName" @load="handleImageLoad" @error="handleImageError" />
        </div>

        <!-- PDF预览 -->
        <div v-else-if="fileType === 'pdf'" class="pdf-preview">
          <div class="online-pdf-preview" v-if="glob.viewUrl">
            <iframe :src="pdfViewerUrl" frameborder="0" width="100%" height="700px"></iframe>
          </div>
          <div v-else class="download-preview">
            <a-result status="info" title="PDF文档预览" sub-title="在线预览服务不可用，请下载文件到本地查看">
              <template #extra>
                <a-button type="primary" @click="downloadFile">
                  <template #icon><DownloadOutlined /></template>
                  下载文件
                </a-button>
              </template>
            </a-result>
          </div>
        </div>

        <!-- Office文档预览 -->
        <div v-else-if="isOfficeFile" class="office-preview">
          <div class="microsoft-office-preview">
            <iframe :src="microsoftOfficeUrl" frameborder="0" width="100%" height="700px"></iframe>
          </div>
        </div>

        <!-- 视频预览 -->
        <div v-else-if="fileType === 'video'" class="video-preview">
          <video :src="fileUrl" controls width="100%" height="400px"> 您的浏览器不支持视频播放 </video>
        </div>

        <!-- 音频预览 -->
        <div v-else-if="fileType === 'audio'" class="audio-preview">
          <audio :src="fileUrl" controls style="width: 100%"> 您的浏览器不支持音频播放 </audio>
        </div>

        <!-- 文本文件预览 -->
        <div v-else-if="fileType === 'text'" class="text-preview">
          <pre>{{ textContent }}</pre>
        </div>

        <!-- 不支持预览的文件类型 -->
        <div v-else class="unsupported-preview">
          <a-result status="info" title="该文件类型暂不支持在线预览" :sub-title="`文件名: ${fileName}`">
            <template #extra>
              <a-button type="primary" @click="handleDownload">下载文件</a-button>
            </template>
          </a-result>
        </div>
      </div>
    </div>
  </CustomModal>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue';
  import { message } from 'ant-design-vue';
  import { DownloadOutlined } from '@ant-design/icons-vue';
  import CustomModal from '/@/components/Modal/src/CustomModal.vue';
  import { useGlobSetting } from '/@/hooks/setting';
  import { encryptByBase64 } from '/@/utils/cipher';
  import { getFileAccessHttpUrl } from '/@/utils/common/compUtils';
  import { getFileblob } from '/@/api/common/api';

  interface FileInfo {
    fileName: string;
    filePath: string;
    fileType?: string;
    fileSize?: number;
  }

  interface Props {
    open?: boolean;
    fileInfo?: FileInfo | null;
  }

  interface Emits {
    (e: 'update:open', value: boolean): void;
    (e: 'close'): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    open: false,
    fileInfo: null,
  });

  const emit = defineEmits<Emits>();

  const glob = useGlobSetting();
  const visible = ref(false);
  const loading = ref(false);
  const error = ref('');
  const errorSubTitle = ref('');
  const textContent = ref('');

  // 监听外部传入的open状态
  watch(
    () => props.open,
    (newValue) => {
      visible.value = newValue;
      if (newValue && props.fileInfo) {
        initPreview();
      }
    },
    { immediate: true }
  );

  // 监听内部visible状态变化
  watch(visible, (newValue) => {
    emit('update:open', newValue);
    if (!newValue) {
      resetState();
    }
  });

  // 文件信息
  const fileName = computed(() => props.fileInfo?.fileName || '');
  const fileUrl = computed(() => {
    const filePath = props.fileInfo?.filePath || '';
    if (!filePath) return '';

    // 使用getFileAccessHttpUrl处理文件路径，确保可以正确访问
    return getFileAccessHttpUrl(filePath);
  });

  // 根据文件扩展名判断文件类型
  const fileType = computed(() => {
    // 优先从文件名获取扩展名，如果文件名没有扩展名则从文件路径获取
    let extension = '';
    if (fileName.value && fileName.value.includes('.')) {
      extension = fileName.value.toLowerCase().split('.').pop() || '';
    } else if (fileUrl.value) {
      extension = fileUrl.value.toLowerCase().split('.').pop() || '';
    }

    if (!extension) return 'other';

    switch (extension) {
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
      case 'svg':
        return 'image';
      case 'pdf':
        return 'pdf';
      case 'doc':
      case 'docx':
        return 'word';
      case 'xls':
      case 'xlsx':
        return 'excel';
      case 'ppt':
      case 'pptx':
        return 'powerpoint';
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
      case 'flv':
      case 'webm':
        return 'video';
      case 'mp3':
      case 'wav':
      case 'flac':
      case 'aac':
      case 'ogg':
        return 'audio';
      case 'txt':
      case 'md':
      case 'json':
      case 'xml':
      case 'csv':
        return 'text';
      default:
        return 'other';
    }
  });

  // 是否为Office文档
  const isOfficeFile = computed(() => {
    return ['word', 'excel', 'powerpoint'].includes(fileType.value);
  });

  // 弹窗标题
  const modalTitle = computed(() => {
    return `文件预览 - ${fileName.value}`;
  });

  // 弹窗宽度
  const modalWidth = computed(() => {
    return '1000px';
  });

  // PDF预览URL
  const pdfViewerUrl = computed(() => {
    if (fileType.value !== 'pdf' || !fileUrl.value) return '';
    const encodedUrl = encodeURIComponent(encryptByBase64(fileUrl.value));
    return `${glob.viewUrl}?url=${encodedUrl}`;
  });

  // Microsoft Office Online预览URL
  const microsoftOfficeUrl = computed(() => {
    if (!isOfficeFile.value || !fileUrl.value) return '';
    return `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fileUrl.value)}`;
  });

  // 初始化预览
  async function initPreview() {
    if (!props.fileInfo) return;

    resetState();
    loading.value = true;

    try {
      // 根据文件类型加载内容
      if (fileType.value === 'text') {
        await loadTextContent();
      }
      // PDF和Office文档使用iframe预览，不需要额外的内容加载
      // 图片、视频、音频也不需要额外的内容加载

      loading.value = false;
    } catch (err) {
      loading.value = false;
      error.value = '文件加载失败';
      errorSubTitle.value = err instanceof Error ? err.message : '请检查文件是否存在或网络连接';
    }
  }

  // 加载文本内容
  async function loadTextContent() {
    try {
      // 使用getFileblob获取文件内容，避免CORS问题
      const blob = await getFileblob(fileUrl.value, {});
      textContent.value = await blob.text();
    } catch (err) {
      throw new Error('无法加载文本内容');
    }
  }

  // 重置状态
  function resetState() {
    loading.value = false;
    error.value = '';
    errorSubTitle.value = '';
    textContent.value = '';
  }

  // 图片加载完成
  function handleImageLoad() {
    loading.value = false;
  }

  // 图片加载失败
  function handleImageError() {
    loading.value = false;
    error.value = '图片加载失败';
    errorSubTitle.value = '请检查图片文件是否存在';
  }

  // 重试
  function handleRetry() {
    initPreview();
  }

  // 下载文件
  function handleDownload() {
    if (!fileUrl.value) {
      message.warning('文件路径不存在');
      return;
    }

    const link = document.createElement('a');
    link.href = fileUrl.value;
    link.download = fileName.value || '文件';
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  // 下载文件（用于模板中的downloadFile方法）
  function downloadFile() {
    handleDownload();
  }

  // 关闭弹窗
  function handleClose() {
    visible.value = false;
    emit('close');
  }
</script>

<style lang="less" scoped>
  :deep(.custom-modal-content) {
    padding: 0 !important;
  }
  .file-preview-container {
    min-height: 400px;

    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 400px;
    }

    .error-container {
      padding: 20px;
    }

    .preview-content {
      .image-preview {
        text-align: center;
        padding: 20px;

        img {
          max-width: 100%;
          max-height: 70vh;
          object-fit: contain;
          border-radius: 4px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }

      .pdf-preview,
      .office-preview {
        iframe {
          border-radius: 4px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }

      .video-preview {
        text-align: center;
        padding: 20px;

        video {
          border-radius: 4px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }

      .audio-preview {
        padding: 40px 20px;
        text-align: center;
      }

      .text-preview {
        padding: 20px;
        max-height: 600px;
        overflow-y: auto;

        pre {
          background: #f5f5f5;
          padding: 16px;
          border-radius: 4px;
          font-family: 'Courier New', monospace;
          font-size: 14px;
          line-height: 1.5;
          white-space: pre-wrap;
          word-wrap: break-word;
        }
      }

      .unsupported-preview {
        padding: 20px;
      }
    }

    // PDF预览样式
    .pdf-preview {
      .pdf-toolbar {
        padding: 10px;
        border-bottom: 1px solid #f0f0f0;
        background: #fafafa;
        text-align: center;
      }

      .pdfjs-preview {
        .pdf-container {
          text-align: center;
          padding: 20px;
          overflow: auto;
          max-height: 600px;

          .pdf-canvas {
            border: 1px solid #ddd;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }
        }
      }
    }

    // Office文档预览样式
    .office-preview {
      .plugin-office-preview {
        .word-container {
          padding: 20px;
          background: white;
          border: 1px solid #ddd;
          border-radius: 4px;
          max-height: 600px;
          overflow: auto;

          :deep(p) {
            margin-bottom: 12px;
            line-height: 1.6;
          }

          :deep(h1),
          :deep(h2),
          :deep(h3),
          :deep(h4),
          :deep(h5),
          :deep(h6) {
            margin-top: 20px;
            margin-bottom: 12px;
            font-weight: bold;
          }

          :deep(table) {
            border-collapse: collapse;
            width: 100%;
            margin: 12px 0;

            td,
            th {
              border: 1px solid #ddd;
              padding: 8px;
              text-align: left;
            }

            th {
              background-color: #f5f5f5;
              font-weight: bold;
            }
          }
        }

        .excel-container {
          max-height: 600px;
          overflow: auto;

          .excel-tabs {
            .ant-tabs-content-holder {
              max-height: 550px;
              overflow: auto;
            }
          }

          .excel-sheet {
            :deep(table) {
              border-collapse: collapse;
              width: 100%;
              font-size: 12px;

              td,
              th {
                border: 1px solid #ddd;
                padding: 4px 8px;
                text-align: left;
                white-space: nowrap;
              }

              th {
                background-color: #f5f5f5;
                font-weight: bold;
              }

              tr:nth-child(even) {
                background-color: #f9f9f9;
              }
            }
          }
        }
      }

      .docx-preview {
        .docx-container {
          padding: 20px;
          background: white;
          border: 1px solid #ddd;
          border-radius: 4px;
          max-height: 600px;
          overflow: auto;

          :deep(.docx-preview-content) {
            font-family: 'Times New Roman', serif;
            line-height: 1.6;

            p {
              margin-bottom: 12px;
            }

            table {
              border-collapse: collapse;
              width: 100%;
              margin: 12px 0;

              td,
              th {
                border: 1px solid #ddd;
                padding: 8px;
              }
            }
          }
        }
      }
    }
  }
</style>
