import { ref } from 'vue';

export interface FileInfo {
  fileName: string;
  filePath: string;
  fileType?: string;
  fileSize?: number;
}

export function useFilePreview() {
  const visible = ref(false);
  const currentFile = ref<FileInfo | null>(null);

  /**
   * 打开文件预览
   * @param fileInfo 文件信息
   */
  function openPreview(fileInfo: FileInfo) {
    currentFile.value = fileInfo;
    visible.value = true;
  }

  /**
   * 关闭文件预览
   */
  function closePreview() {
    visible.value = false;
    currentFile.value = null;
  }

  /**
   * 根据文件扩展名判断是否支持预览
   * @param filePath 文件名
   * @returns 是否支持预览
   */
  function isSupportPreview(filePath: string): boolean {
    console.log('useFilePreview: isSupportPreview called with', filePath);
    if (!filePath) return false;

    const extension = filePath.toLowerCase().split('.').pop();
    const supportedExtensions = [
      // 图片
      'jpg',
      'jpeg',
      'png',
      'gif',
      'bmp',
      'webp',
      'svg',
      // 文档
      'pdf',
      'doc',
      'docx',
      'xls',
      'xlsx',
      'ppt',
      'pptx',
      // 媒体
      'mp4',
      'avi',
      'mov',
      'wmv',
      'flv',
      'webm',
      'mp3',
      'wav',
      'flac',
      'aac',
      'ogg',
      // 文本
      'txt',
      'md',
      'json',
      'xml',
      'csv',
    ];

    const isSupported = supportedExtensions.includes(extension || '');
    console.log('isSupportPreview:', filePath, 'extension:', extension, 'supported:', isSupported);
    return isSupported;
  }

  /**
   * 根据文件扩展名获取文件类型
   * @param fileName 文件名
   * @returns 文件类型
   */
  function getFileType(fileName: string): string {
    if (!fileName) return 'other';

    const extension = fileName.toLowerCase().split('.').pop();

    switch (extension) {
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
      case 'svg':
        return 'image';
      case 'pdf':
        return 'pdf';
      case 'doc':
      case 'docx':
        return 'word';
      case 'xls':
      case 'xlsx':
        return 'excel';
      case 'ppt':
      case 'pptx':
        return 'powerpoint';
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
      case 'flv':
      case 'webm':
        return 'video';
      case 'mp3':
      case 'wav':
      case 'flac':
      case 'aac':
      case 'ogg':
        return 'audio';
      case 'txt':
      case 'md':
      case 'json':
      case 'xml':
      case 'csv':
        return 'text';
      default:
        return 'other';
    }
  }

  return {
    visible,
    currentFile,
    openPreview,
    closePreview,
    isSupportPreview,
    getFileType,
  };
}
