<template>
  <!-- 新增/编辑弹窗 -->
  <CustomModal
    v-model:open="visible"
    :title="modalTitle"
    width="800px"
    :show-footer="true"
    :show-cancel-button="true"
    :show-confirm-button="!isViewMode"
    cancel-text="取消"
    confirm-text="确认"
    @confirm="handleSubmit"
    @cancel="handleCancel"
  >
    <div class="modal-content">
      <a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical" :disabled="isViewMode">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="模板标题" name="templateName">
              <a-input v-model:value="formData.templateName" placeholder="请输入模板标题" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="模板编码" name="templateCode">
              <a-input v-model:value="formData.templateCode" placeholder="请输入模板编码" :disabled="isUpdate" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="模板类型" name="templateType">
              <a-select v-model:value="formData.templateType" placeholder="请选择模板类型" @change="handleTemplateTypeChange">
                <a-select-option value="1">文本模板</a-select-option>
                <a-select-option value="2">富文本模板</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="是否应用" name="useStatus">
              <a-switch v-model:checked="useStatusChecked" checked-children="是" un-checked-children="否" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="模板内容" name="templateContent">
          <!-- 文本模板使用多行文本框 -->
          <a-textarea
            v-if="formData.templateType === '1'"
            v-model:value="formData.templateContent"
            placeholder="请输入模板内容"
            :rows="6"
            :maxlength="500"
            show-count
          />
          <!-- 富文本模板使用富文本编辑器 -->
          <JEditorTiptap
            v-else-if="formData.templateType === '2'"
            v-model:value="formData.templateContent"
            height="300px"
            placeholder="请输入模板内容..."
          />
          <!-- 未选择类型时的提示 -->
          <div v-else class="template-type-tip">
            <a-empty description="请先选择模板类型" />
          </div>
        </a-form-item>
      </a-form>
    </div>
  </CustomModal>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, watch } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import CustomModal from '/@/components/Modal/src/CustomModal.vue';
  import JEditorTiptap from '/@/components/Form/src/jeecg/components/JEditorTiptap.vue';
  import { saveOrUpdate } from '/@/views/system/message/template/template.api';

  const { createMessage } = useMessage();

  interface Props {
    open: boolean;
    title: string;
    record?: any;
    isUpdate?: boolean;
    isViewMode?: boolean;
  }

  interface Emits {
    (e: 'update:open', value: boolean): void;
    (e: 'success'): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    open: false,
    title: '新增消息模板',
    record: () => ({}),
    isUpdate: false,
    isViewMode: false,
  });

  const emit = defineEmits<Emits>();

  const visible = computed({
    get: () => props.open,
    set: (value: boolean) => emit('update:open', value),
  });

  const modalTitle = computed(() => props.title);
  const isUpdate = computed(() => props.isUpdate);
  const isViewMode = computed(() => props.isViewMode);

  const formRef = ref();

  // 表单数据
  const formData = reactive({
    id: '',
    templateName: '',
    templateCode: '',
    templateType: '',
    templateContent: '',
    useStatus: '0',
  });

  // 是否应用的计算属性
  const useStatusChecked = computed({
    get: () => formData.useStatus === '1',
    set: (val: boolean) => {
      formData.useStatus = val ? '1' : '0';
    },
  });

  // 表单验证规则
  const formRules = {
    templateName: [{ required: true, message: '请输入模板标题', trigger: 'blur' }],
    templateCode: [{ required: true, message: '请输入模板编码', trigger: 'blur' }],
    templateType: [{ required: true, message: '请选择模板类型', trigger: 'change' }],
    templateContent: [{ required: true, message: '请输入模板内容', trigger: 'blur' }],
  };

  // 重置表单
  const resetForm = () => {
    Object.assign(formData, {
      id: '',
      templateName: '',
      templateCode: '',
      templateType: '',
      templateContent: '',
      useStatus: '0',
    });
  };

  // 监听record变化，更新表单数据
  watch(
    () => props.record,
    (newRecord) => {
      if (newRecord && typeof newRecord === 'object' && Object.keys(newRecord).length > 0) {
        resetForm();
        Object.assign(formData, newRecord);
      } else {
        resetForm();
      }
    },
    { immediate: true }
  );

  // 模板类型变化处理
  const handleTemplateTypeChange = () => {
    // 切换模板类型时清空内容
    formData.templateContent = '';
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      await formRef.value?.validate();

      const params = { ...formData };
      await saveOrUpdate(params, isUpdate.value);

      createMessage.success(isUpdate.value ? '编辑成功' : '新增成功');
      visible.value = false;
      emit('success');
    } catch (error) {
      console.error('提交失败:', error);
    }
  };

  // 取消弹窗
  const handleCancel = () => {
    visible.value = false;
  };
</script>

<style lang="less" scoped>
  .modal-content {
    padding: 16px 0;
  }

  .template-type-tip {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100px;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    background-color: #fafafa;
  }

  :deep(.ant-form-item-label) {
    font-weight: 500;
  }
</style>
