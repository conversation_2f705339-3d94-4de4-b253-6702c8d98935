import { defHttp } from '/@/utils/http/axios';

enum Api {
  QueryAllSend = '/receive/hgyReceive/queryAllSend',
}

/**
 * 查询发件箱消息列表
 */
export const queryAllSend = (params: {
  pageNo: number;
  pageSize: number;
  sendUserId: string;
  keywords?: string;
}) => {
  return defHttp.post<{
    records: any[];
    total: number;
    size: number;
    current: number;
    pages: number;
  }>({
    url: Api.QueryAllSend,
    data: params,
  });
};
