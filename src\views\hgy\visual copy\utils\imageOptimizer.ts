/**
 * 图片优化工具
 * 用于数据大屏图片的预加载、压缩和缓存管理
 */

/**
 * 图片格式转换和压缩
 */
export class ImageOptimizer {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;
  private cache: Map<string, string> = new Map();

  constructor() {
    this.canvas = document.createElement('canvas');
    this.ctx = this.canvas.getContext('2d')!;
  }

  /**
   * 压缩图片
   */
  async compressImage(
    src: string, 
    quality: number = 0.8, 
    maxWidth: number = 1920,
    maxHeight: number = 1080
  ): Promise<string> {
    const cacheKey = `${src}_${quality}_${maxWidth}_${maxHeight}`;
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    return new Promise((resolve, reject) => {
      const img = new Image();
      img.crossOrigin = 'anonymous';
      
      img.onload = () => {
        // 计算压缩后的尺寸
        let { width, height } = this.calculateSize(img.width, img.height, maxWidth, maxHeight);
        
        this.canvas.width = width;
        this.canvas.height = height;
        
        // 绘制压缩后的图片
        this.ctx.drawImage(img, 0, 0, width, height);
        
        // 转换为WebP格式（如果支持）或JPEG
        const format = this.supportsWebP() ? 'image/webp' : 'image/jpeg';
        const compressedDataUrl = this.canvas.toDataURL(format, quality);
        
        this.cache.set(cacheKey, compressedDataUrl);
        resolve(compressedDataUrl);
      };
      
      img.onerror = () => reject(new Error(`Failed to load image: ${src}`));
      img.src = src;
    });
  }

  /**
   * 计算压缩尺寸
   */
  private calculateSize(
    originalWidth: number, 
    originalHeight: number, 
    maxWidth: number, 
    maxHeight: number
  ): { width: number; height: number } {
    let width = originalWidth;
    let height = originalHeight;

    if (width > maxWidth) {
      height = (height * maxWidth) / width;
      width = maxWidth;
    }

    if (height > maxHeight) {
      width = (width * maxHeight) / height;
      height = maxHeight;
    }

    return { width: Math.round(width), height: Math.round(height) };
  }

  /**
   * 检查是否支持WebP
   */
  private supportsWebP(): boolean {
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * 获取缓存大小
   */
  getCacheSize(): number {
    return this.cache.size;
  }
}

/**
 * 渐进式图片加载器
 */
export class ProgressiveImageLoader {
  private loadingImages: Set<string> = new Set();
  private loadedImages: Map<string, HTMLImageElement> = new Map();
  private observers: Map<string, IntersectionObserver> = new Map();

  /**
   * 创建渐进式加载的图片元素
   */
  createProgressiveImage(
    container: HTMLElement,
    src: string,
    placeholder?: string,
    options?: {
      quality?: number;
      maxWidth?: number;
      maxHeight?: number;
      lazy?: boolean;
    }
  ): HTMLImageElement {
    const img = document.createElement('img');
    const { quality = 0.8, maxWidth = 1920, maxHeight = 1080, lazy = true } = options || {};

    // 设置占位符
    if (placeholder) {
      img.src = placeholder;
    } else {
      // 创建简单的占位符
      img.src = this.createPlaceholder(200, 150);
    }

    img.style.transition = 'opacity 0.3s ease';
    img.style.opacity = '0.5';

    if (lazy) {
      // 懒加载
      this.setupLazyLoading(img, src, quality, maxWidth, maxHeight);
    } else {
      // 立即加载
      this.loadImage(img, src, quality, maxWidth, maxHeight);
    }

    container.appendChild(img);
    return img;
  }

  /**
   * 设置懒加载
   */
  private setupLazyLoading(
    img: HTMLImageElement,
    src: string,
    quality: number,
    maxWidth: number,
    maxHeight: number
  ): void {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            this.loadImage(img, src, quality, maxWidth, maxHeight);
            observer.unobserve(img);
            this.observers.delete(src);
          }
        });
      },
      { rootMargin: '50px' }
    );

    observer.observe(img);
    this.observers.set(src, observer);
  }

  /**
   * 加载图片
   */
  private async loadImage(
    img: HTMLImageElement,
    src: string,
    quality: number,
    maxWidth: number,
    maxHeight: number
  ): Promise<void> {
    if (this.loadingImages.has(src)) {
      return;
    }

    this.loadingImages.add(src);

    try {
      // 如果已经加载过，直接使用缓存
      if (this.loadedImages.has(src)) {
        const cachedImg = this.loadedImages.get(src)!;
        img.src = cachedImg.src;
        img.style.opacity = '1';
        return;
      }

      // 使用图片优化器压缩图片
      const optimizer = new ImageOptimizer();
      const compressedSrc = await optimizer.compressImage(src, quality, maxWidth, maxHeight);
      
      // 创建新的图片对象用于预加载
      const newImg = new Image();
      newImg.onload = () => {
        img.src = compressedSrc;
        img.style.opacity = '1';
        this.loadedImages.set(src, newImg);
        this.loadingImages.delete(src);
      };
      
      newImg.onerror = () => {
        console.warn(`Failed to load image: ${src}`);
        this.loadingImages.delete(src);
      };
      
      newImg.src = compressedSrc;
    } catch (error) {
      console.error(`Error loading image ${src}:`, error);
      this.loadingImages.delete(src);
    }
  }

  /**
   * 创建占位符
   */
  private createPlaceholder(width: number, height: number): string {
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext('2d')!;
    
    // 创建渐变背景
    const gradient = ctx.createLinearGradient(0, 0, width, height);
    gradient.addColorStop(0, '#0a0e27');
    gradient.addColorStop(1, '#1a1a2e');
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);
    
    return canvas.toDataURL();
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
    this.loadingImages.clear();
    this.loadedImages.clear();
  }
}

/**
 * 图片预加载管理器
 */
export class ImagePreloadManager {
  private preloadQueue: string[] = [];
  private preloadedImages: Map<string, HTMLImageElement> = new Map();
  private isPreloading = false;
  private optimizer = new ImageOptimizer();

  /**
   * 添加到预加载队列
   */
  addToQueue(src: string): void {
    if (!this.preloadQueue.includes(src) && !this.preloadedImages.has(src)) {
      this.preloadQueue.push(src);
    }
  }

  /**
   * 批量添加到预加载队列
   */
  addBatchToQueue(srcs: string[]): void {
    srcs.forEach(src => this.addToQueue(src));
  }

  /**
   * 开始预加载
   */
  async startPreload(
    options?: {
      batchSize?: number;
      delay?: number;
      quality?: number;
      maxWidth?: number;
      maxHeight?: number;
    }
  ): Promise<void> {
    if (this.isPreloading) {
      return;
    }

    const { 
      batchSize = 3, 
      delay = 100, 
      quality = 0.8, 
      maxWidth = 1920, 
      maxHeight = 1080 
    } = options || {};

    this.isPreloading = true;

    try {
      for (let i = 0; i < this.preloadQueue.length; i += batchSize) {
        const batch = this.preloadQueue.slice(i, i + batchSize);
        
        await Promise.all(
          batch.map(async (src) => {
            try {
              const compressedSrc = await this.optimizer.compressImage(src, quality, maxWidth, maxHeight);
              const img = new Image();
              
              return new Promise<void>((resolve, reject) => {
                img.onload = () => {
                  this.preloadedImages.set(src, img);
                  resolve();
                };
                img.onerror = () => reject(new Error(`Failed to preload: ${src}`));
                img.src = compressedSrc;
              });
            } catch (error) {
              console.warn(`Failed to optimize image: ${src}`, error);
            }
          })
        );

        // 批次间延迟，避免阻塞主线程
        if (i + batchSize < this.preloadQueue.length && delay > 0) {
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    } finally {
      this.isPreloading = false;
      this.preloadQueue = [];
    }
  }

  /**
   * 获取预加载的图片
   */
  getPreloadedImage(src: string): HTMLImageElement | null {
    return this.preloadedImages.get(src) || null;
  }

  /**
   * 清理缓存
   */
  cleanup(): void {
    this.preloadedImages.clear();
    this.preloadQueue = [];
    this.optimizer.clearCache();
  }
}

// 导出单例实例
export const imageOptimizer = new ImageOptimizer();
export const progressiveImageLoader = new ProgressiveImageLoader();
export const imagePreloadManager = new ImagePreloadManager();
