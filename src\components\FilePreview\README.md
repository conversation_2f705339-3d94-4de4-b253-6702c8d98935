# FilePreview 文件预览组件

一个支持多种文件类型预览的Vue组件，基于CustomModal弹窗实现。

## 功能特性

- 🖼️ **图片预览**：支持 jpg、jpeg、png、gif、bmp、webp、svg 格式
- 📄 **PDF预览**：
  - **PDF.js插件预览**（推荐）：使用PDF.js库实现高质量预览，支持翻页、缩放等功能
  - 浏览器原生预览：直接使用浏览器内置PDF查看器
  - kkfileview在线预览（需要配置viewUrl）
  - 下载到本地查看
- 📊 **Office文档预览**：支持 doc、docx、xls、xlsx、ppt、pptx 格式
  - **插件预览**（推荐）：
    - Word文档：使用mammoth.js转换为HTML格式预览
    - Excel文档：使用xlsx库解析并以表格形式展示，支持多工作表
  - **DOCX预览**：使用docx-preview库实现更精确的Word文档预览
  - kkfileview在线预览（需要配置viewUrl）
  - Microsoft Office Online预览
  - Google Docs预览
  - 下载到本地查看
- 🎥 **视频预览**：支持 mp4、avi、mov、wmv、flv、webm 格式
- 🎵 **音频预览**：支持 mp3、wav、flac、aac、ogg 格式
- 📝 **文本预览**：支持 txt、md、json、xml、csv 格式
- 📥 **下载功能**：不支持预览的文件可直接下载
- 🔄 **错误重试**：预览失败时提供重试功能
- 📑 **多种预览方式**：为PDF和Office文档提供多种预览选项

## 基本用法

### 1. 直接使用组件

```vue
<template>
  <div>
    <a-button @click="openPreview">预览文件</a-button>

    <FilePreview v-model:open="visible" :file-info="fileInfo" @close="handleClose" />
  </div>
</template>

<script setup>
  import { ref } from 'vue';
  import FilePreview from '/@/components/FilePreview';

  const visible = ref(false);
  const fileInfo = ref({
    fileName: 'example.pdf',
    filePath: 'https://example.com/files/example.pdf',
    fileType: 'pdf',
  });

  function openPreview() {
    visible.value = true;
  }

  function handleClose() {
    visible.value = false;
  }
</script>
```

### 2. 使用组合式函数

```vue
<template>
  <div>
    <a-button @click="handlePreview">预览文件</a-button>

    <FilePreview v-model:open="previewVisible" :file-info="currentFile" @close="closePreview" />
  </div>
</template>

<script setup>
  import FilePreview from '/@/components/FilePreview';
  import { useFilePreview } from '/@/components/FilePreview/src/useFilePreview';

  const { visible: previewVisible, currentFile, openPreview, closePreview, isSupportPreview } = useFilePreview();

  function handlePreview() {
    const fileInfo = {
      fileName: 'document.docx',
      filePath: 'https://example.com/files/document.docx',
      fileType: 'docx',
    };

    if (isSupportPreview(fileInfo.fileName)) {
      openPreview(fileInfo);
    } else {
      // 处理不支持预览的文件
      console.log('该文件类型不支持预览');
    }
  }
</script>
```

## API

### FilePreview Props

| 参数     | 说明             | 类型               | 默认值  |
| -------- | ---------------- | ------------------ | ------- |
| open     | 是否显示预览弹窗 | `boolean`          | `false` |
| fileInfo | 文件信息对象     | `FileInfo \| null` | `null`  |

### FileInfo 接口

```typescript
interface FileInfo {
  fileName: string; // 文件名
  filePath: string; // 文件路径/URL
  fileType?: string; // 文件类型（可选）
  fileSize?: number; // 文件大小（可选）
}
```

### FilePreview Events

| 事件名      | 说明             | 回调参数           |
| ----------- | ---------------- | ------------------ |
| update:open | 弹窗显示状态变化 | `(value: boolean)` |
| close       | 关闭弹窗         | `()`               |

### useFilePreview 返回值

| 属性/方法        | 说明                 | 类型                            |
| ---------------- | -------------------- | ------------------------------- |
| visible          | 预览弹窗显示状态     | `Ref<boolean>`                  |
| currentFile      | 当前预览的文件信息   | `Ref<FileInfo \| null>`         |
| openPreview      | 打开文件预览         | `(fileInfo: FileInfo) => void`  |
| closePreview     | 关闭文件预览         | `() => void`                    |
| isSupportPreview | 判断文件是否支持预览 | `(fileName: string) => boolean` |
| getFileType      | 获取文件类型         | `(fileName: string) => string`  |

## 支持的文件类型

### 图片文件

- jpg, jpeg, png, gif, bmp, webp, svg

### 文档文件

- pdf
- doc, docx (Word文档)
- xls, xlsx (Excel表格)
- ppt, pptx (PowerPoint演示文稿)

### 媒体文件

- mp4, avi, mov, wmv, flv, webm (视频)
- mp3, wav, flac, aac, ogg (音频)

### 文本文件

- txt, md, json, xml, csv

## 预览方式说明

### PDF文档预览

1. **PDF.js插件预览**（默认推荐）：
   - 使用PDF.js库实现客户端预览，无需服务器支持
   - 支持翻页、缩放、重置等功能
   - 渲染质量高，加载速度快
   - 完全离线工作，安全性好

2. **浏览器原生预览**：直接使用浏览器内置的PDF查看器，兼容性最好

3. **kkfileview在线预览**：需要配置全局的`viewUrl`，支持更多功能

4. **下载查看**：下载到本地使用专业软件查看

### Office文档预览

1. **插件预览**（默认推荐）：
   - **Word文档**：使用mammoth.js将.doc/.docx转换为HTML格式
     - 支持文本、段落、标题、表格等基本格式
     - 保持原文档的基本布局和样式
     - 客户端处理，无需服务器支持
   - **Excel文档**：使用xlsx库解析.xls/.xlsx文件
     - 支持多工作表切换
     - 以HTML表格形式展示数据
     - 保持单元格格式和数据类型

2. **DOCX预览**（仅Word文档）：
   - 使用docx-preview库实现更精确的预览
   - 更好地保持原文档格式和布局
   - 支持更复杂的文档结构

3. **kkfileview在线预览**：需要配置全局的`viewUrl`，转换效果最好

4. **Microsoft Office Online**：使用微软官方在线预览服务，需要文件可公网访问

5. **Google Docs预览**：使用Google的在线预览服务，需要文件可公网访问

6. **下载查看**：下载到本地使用Office软件查看

## 注意事项

1. **文件访问权限**：使用在线预览服务时，文件必须可以通过公网URL访问
2. **跨域问题**：确保文件服务器支持跨域访问
3. **文件大小**：大文件可能需要较长加载时间，建议提供下载选项
4. **浏览器兼容性**：某些文件格式的预览依赖浏览器原生支持
5. **网络环境**：Microsoft Office Online和Google Docs预览需要良好的网络环境

## 自定义样式

组件提供了完整的CSS类名，可以通过覆盖样式来自定义外观：

```less
.file-preview-container {
  .image-preview img {
    border-radius: 8px;
  }

  .text-preview pre {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
  }
}
```
