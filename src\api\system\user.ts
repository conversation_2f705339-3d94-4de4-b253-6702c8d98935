import { defHttp } from '/@/utils/http/axios';

/**
 * 用户相关API接口
 */
enum Api {
  // 注销账号
  unsubscribe = '/hgyUser/unsubscribe',
  // 获取验证码
  getSmsCode = '/hgyUser/hgysms',
}

/**
 * 注销账号参数接口
 */
export interface UnsubscribeParams {
  phone: string;
  code: string;
}

/**
 * 获取验证码参数接口
 */
export interface GetSmsCodeParams {
  mobile: string;
}

/**
 * 注销账号
 * @param params 注销参数
 * @returns Promise<any>
 */
export const unsubscribeAccount = (params: UnsubscribeParams) => {
  return defHttp.get({
    url: Api.unsubscribe,
    params,
  });
};

/**
 * 获取验证码
 * @param params 获取验证码参数
 * @returns Promise<any>
 */
export const getSmsCode = (params: GetSmsCodeParams) => {
  return defHttp.post({
    url: Api.getSmsCode,
    data: params,
  });
};
