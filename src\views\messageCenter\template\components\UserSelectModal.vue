<template>
  <!-- 用户选择弹窗 -->
  <CustomModal
    v-model:open="visible"
    title="选择用户"
    width="1600px"
    :show-footer="true"
    :show-cancel-button="true"
    :show-confirm-button="true"
    cancel-text="取消"
    confirm-text="确认"
    @confirm="handleConfirmSelectUser"
    @cancel="handleCancel"
  >
    <div class="user-select-content">
      <BasicTable @register="registerUserTable" :row-selection="userRowSelection">
        <!-- 用户头像列 -->
        <template #avatar="{ text }">
          <a-avatar :src="text" v-if="text">
            <template #icon>
              <UserOutlined />
            </template>
          </a-avatar>
          <a-avatar v-else>
            <template #icon>
              <UserOutlined />
            </template>
          </a-avatar>
        </template>
      </BasicTable>
    </div>
  </CustomModal>
</template>

<script lang="ts" setup>
  import { ref, computed, watch } from 'vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import { BasicColumn } from '/@/components/Table';
  import { useMessage } from '/@/hooks/web/useMessage';
  import CustomModal from '/@/components/Modal/src/CustomModal.vue';
  import { list as getUserList } from '/@/views/system/user/user.api';
  import { UserOutlined } from '@ant-design/icons-vue';

  const { createMessage } = useMessage();

  interface Props {
    open: boolean;
    selectedUsers?: any[]; // 已选中的用户数据
  }

  interface Emits {
    (e: 'update:open', value: boolean): void;
    (e: 'confirm', selectedUsers: any[]): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    open: false,
    selectedUsers: () => [],
  });

  const emit = defineEmits<Emits>();

  const visible = computed({
    get: () => props.open,
    set: (value: boolean) => emit('update:open', value),
  });

  // 用户选择相关状态
  const selectedUsers = ref<any[]>([]);
  const selectedUserKeys = ref<string[]>([]);

  // 监听props中的selectedUsers变化，用于回显数据
  watch(
    () => props.selectedUsers,
    (newSelectedUsers) => {
      if (newSelectedUsers && newSelectedUsers.length > 0) {
        selectedUsers.value = [...newSelectedUsers];
        selectedUserKeys.value = newSelectedUsers.map((user) => user.username);
      } else {
        selectedUsers.value = [];
        selectedUserKeys.value = [];
      }
    },
    { immediate: true, deep: true }
  );

  // 用户表格列配置
  const userColumns: BasicColumn[] = [
    {
      title: '用户账号',
      dataIndex: 'username',
      width: 120,
    },
    {
      title: '用户姓名',
      dataIndex: 'realname',
      width: 120,
    },
    {
      title: '头像',
      dataIndex: 'avatar',
      width: 80,
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      width: 120,
    },
  ];

  // 用户表格配置
  const [registerUserTable] = useTable({
    api: getUserList,
    columns: userColumns,
    striped: false,
    useSearchForm: true,
    showTableSetting: false,
    bordered: false,
    showIndexColumn: false,
    canResize: true,
    inset: true,
    maxHeight: 500,
    rowKey: 'username',
    formConfig: {
      labelWidth: 64,
      size: 'large',
      actionColOptions: {
        span: 6,
        style: {
          textAlign: 'left',
        },
      },
      schemas: [
        {
          field: 'username',
          label: '用户账号',
          component: 'Input',
          componentProps: {
            placeholder: '请输入账号',
          },
          colProps: { span: 6 },
        },
        {
          field: 'realname',
          label: '用户姓名',
          component: 'Input',
          componentProps: {
            placeholder: '请输入姓名',
          },
          colProps: { span: 6 },
        },
      ],
    },
  });

  // 用户行选择配置
  const userRowSelection = computed(() => ({
    type: 'checkbox' as const,
    selectedRowKeys: selectedUserKeys.value,
    preserveSelectedRowKeys: true, // 保持跨页选择
    onChange: (selectedRowKeys: string[], selectedRows: any[]) => {
      selectedUserKeys.value = selectedRowKeys;

      // 处理跨页选择：合并当前页选中的数据和之前页面的数据
      const currentPageUsers = selectedRows;
      const otherPageUsers = selectedUsers.value.filter((user) => !currentPageUsers.some((currentUser) => currentUser.username === user.username));

      // 只保留仍然被选中的其他页面用户
      const validOtherPageUsers = otherPageUsers.filter((user) => selectedRowKeys.includes(user.username));

      selectedUsers.value = [...validOtherPageUsers, ...currentPageUsers];
    },
    onSelect: (record: any, selected: boolean) => {
      if (selected) {
        // 添加选中的用户
        if (!selectedUsers.value.some((user) => user.username === record.username)) {
          selectedUsers.value.push(record);
        }
      } else {
        // 移除取消选中的用户
        selectedUsers.value = selectedUsers.value.filter((user) => user.username !== record.username);
      }
    },
    onSelectAll: (selected: boolean, _selectedRows: any[], changeRows: any[]) => {
      if (selected) {
        // 全选：添加当前页所有用户
        changeRows.forEach((row) => {
          if (!selectedUsers.value.some((user) => user.username === row.username)) {
            selectedUsers.value.push(row);
          }
        });
      } else {
        // 取消全选：移除当前页所有用户
        const changeUsernames = changeRows.map((row) => row.username);
        selectedUsers.value = selectedUsers.value.filter((user) => !changeUsernames.includes(user.username));
      }
    },
  }));

  // 确认选择用户
  const handleConfirmSelectUser = () => {
    if (selectedUsers.value.length === 0) {
      createMessage.warning('请选择至少一个用户');
      return;
    }

    emit('confirm', selectedUsers.value);
    visible.value = false;

    // 清空选择状态
    selectedUsers.value = [];
    selectedUserKeys.value = [];
  };

  // 取消选择用户
  const handleCancel = () => {
    visible.value = false;
    // 取消时恢复到原始状态
    if (props.selectedUsers && props.selectedUsers.length > 0) {
      selectedUsers.value = [...props.selectedUsers];
      selectedUserKeys.value = props.selectedUsers.map((user) => user.username);
    } else {
      selectedUsers.value = [];
      selectedUserKeys.value = [];
    }
  };
</script>

<style lang="less" scoped>
  .user-select-content {
    padding: 0;
  }
  :deep(.ant-table-tbody > tr > td) {
    vertical-align: top;
  }
  :deep(.jeecg-basic-table-form-container) {
    padding: 0;
  }
  :deep(.table-content) {
    padding: 0;
  }

  // 自定义选中行的背景色 - 浅灰色
  :deep(.ant-table-tbody > tr.ant-table-row-selected > td) {
    background-color: rgba(0, 0, 0, 0.03) !important;
  }

  // 鼠标悬停时的背景色
  :deep(.ant-table-tbody > tr.ant-table-row-selected:hover > td) {
    background-color: rgba(0, 0, 0, 0.05) !important;
  }

  // 复选框选中行的背景色
  :deep(.ant-table-row-selected) {
    background-color: rgba(0, 0, 0, 0.03) !important;
  }

  // 复选框选中行悬停时的背景色
  :deep(.ant-table-row-selected:hover) {
    background-color: rgba(0, 0, 0, 0.05) !important;
  }
</style>
