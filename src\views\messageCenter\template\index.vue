<template>
  <div class="p-4">
    <BasicTable @register="registerTable">
      <!-- 表格工具栏 -->
      <template #tableTitle>
        <a-button type="primary" @click="handleAdd">
          <template #icon>
            <PlusOutlined />
          </template>
          新增
        </a-button>
      </template>

      <!-- 操作栏 -->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>

      <!-- 模板类型列 -->
      <template #templateType="{ text }">
        <a-tag :color="getTemplateTypeColor(text)">
          {{ getTemplateTypeText(text) }}
        </a-tag>
      </template>

      <!-- 是否应用列 -->
      <template #useStatus="{ text }">
        <a-tag :color="text === '1' ? 'green' : 'default'">
          {{ text === '1' ? '是' : '否' }}
        </a-tag>
      </template>

      <!-- 模板内容列 -->
      <template #templateContent="{ text }">
        <div class="template-content-preview">
          <span v-if="text && text.length > 50">{{ text.substring(0, 50) }}...</span>
          <span v-else>{{ text || '-' }}</span>
        </div>
      </template>
    </BasicTable>

    <!-- 新增/编辑弹窗 -->
    <TemplateModal
      v-model:open="modalVisible"
      :title="modalTitle"
      :record="currentRecord"
      :is-update="isUpdate"
      :is-view-mode="isViewMode"
      @success="handleModalSuccess"
    />

    <!-- 发送测试弹窗 -->
    <SendTestModal v-model:open="sendModalVisible" :record="currentSendRecord" />
  </div>
</template>

<script lang="ts" setup name="MessageTemplate">
  import { ref, computed } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { ActionItem, BasicColumn } from '/@/components/Table';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { list, deleteBatch, saveOrUpdate } from '/@/views/system/message/template/template.api';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import TemplateModal from './components/TemplateModal.vue';
  import SendTestModal from './components/SendTestModal.vue';

  const { createMessage } = useMessage();

  // 表格列配置
  const columns: BasicColumn[] = [
    {
      title: '模板标题',
      dataIndex: 'templateName',
      width: 150,
    },
    {
      title: '模板编码',
      dataIndex: 'templateCode',
      width: 150,
    },
    {
      title: '模板类型',
      dataIndex: 'templateType',
      width: 120,
    },
    {
      title: '模板内容',
      dataIndex: 'templateContent',
      width: 200,
    },
    {
      title: '是否应用',
      dataIndex: 'useStatus',
      width: 100,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
    },
  ];

  // 表格配置
  const [registerTable, { reload }] = useTable({
    api: list,
    columns,
    striped: false,
    useSearchForm: true,
    showTableSetting: false,
    bordered: false,
    showIndexColumn: false,
    canResize: true,
    inset: true,
    maxHeight: 468,
    actionColumn: {
      width: 280,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      fixed: 'right',
    },
    formConfig: {
      labelWidth: 64,
      size: 'large',
      schemas: [
        {
          field: 'templateName',
          label: '模板标题',
          component: 'Input',
          componentProps: {
            placeholder: '请输入模板标题',
          },
          colProps: { span: 6 },
        },
        {
          field: 'templateCode',
          label: '模板编码',
          component: 'Input',
          componentProps: {
            placeholder: '请输入模板编码',
          },
          colProps: { span: 6 },
        },
        {
          field: 'templateType',
          label: '模板类型',
          component: 'Select',
          componentProps: {
            placeholder: '请选择模板类型',
            options: [
              { label: '文本模板', value: '1' },
              { label: '富文本模板', value: '2' },
            ],
          },
          colProps: { span: 6 },
        },
      ],
    },
  });

  // 弹窗相关状态
  const modalVisible = ref(false);
  const modalTitle = ref('');
  const isUpdate = ref(false);
  const isViewMode = ref(false);
  const currentRecord = ref({});

  // 发送测试弹窗相关状态
  const sendModalVisible = ref(false);
  const currentSendRecord = ref({});

  // 获取模板类型颜色
  const getTemplateTypeColor = (type: string) => {
    const colorMap = {
      '1': 'blue', // 文本模板
      '2': 'green', // 富文本模板
    };
    return colorMap[type] || 'default';
  };

  // 获取模板类型文本
  const getTemplateTypeText = (type: string) => {
    const textMap = {
      '1': '文本模板',
      '2': '富文本模板',
    };
    return textMap[type] || '未知';
  };

  // 获取表格操作按钮
  const getTableAction = (record: any): ActionItem[] => {
    const actions: ActionItem[] = [
      {
        label: '查看',
        onClick: () => handleView(record),
      },
      {
        label: '编辑',
        onClick: () => handleEdit(record),
        ifShow: record.useStatus !== '1', // 已应用的模板不能编辑
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '确定要删除这个模板吗？',
          placement: 'left',
          confirm: () => handleDelete(record),
        },
        ifShow: record.useStatus !== '1', // 已应用的模板不能删除
      },
      {
        label: record.useStatus === '1' ? '停用' : '应用',
        color: record.useStatus === '1' ? 'warning' : 'success',
        onClick: () => handleToggleStatus(record),
      },
      {
        label: '发送',
        onClick: () => handleSend(record),
      },
    ];
    return actions;
  };

  // 新增
  const handleAdd = () => {
    currentRecord.value = {};
    modalTitle.value = '新增消息模板';
    isUpdate.value = false;
    isViewMode.value = false;
    modalVisible.value = true;
  };

  // 查看
  const handleView = (record: any) => {
    currentRecord.value = { ...record };
    modalTitle.value = '查看消息模板';
    isUpdate.value = true;
    isViewMode.value = true;
    modalVisible.value = true;
  };

  // 编辑
  const handleEdit = (record: any) => {
    if (record.useStatus === '1') {
      createMessage.warning('此模板已被应用，禁止编辑!');
      return;
    }
    currentRecord.value = { ...record };
    modalTitle.value = '编辑消息模板';
    isUpdate.value = true;
    isViewMode.value = false;
    modalVisible.value = true;
  };

  // 删除
  const handleDelete = async (record: any) => {
    try {
      await deleteBatch({ ids: record.id }, false);
      reload();
    } catch (error) {
      console.error('删除失败:', error);
    }
  };

  // 切换应用状态
  const handleToggleStatus = async (record: any) => {
    try {
      const newStatus = record.useStatus === '1' ? '0' : '1';

      await saveOrUpdate({ id: record.id, useStatus: newStatus }, true);
      reload();
    } catch (error) {
      console.error('操作失败:', error);
    }
  };

  // 发送测试
  const handleSend = (record: any) => {
    currentSendRecord.value = { ...record };
    sendModalVisible.value = true;
  };

  // 弹窗成功回调
  const handleModalSuccess = () => {
    reload();
  };
</script>

<style lang="less" scoped>
  .template-content-preview {
    max-width: 200px;
    word-break: break-all;
    line-height: 1.4;
  }

  :deep(.ant-table-tbody > tr > td) {
    vertical-align: top;
  }

  :deep(.ant-tag) {
    margin-right: 0;
  }

  :deep(.ant-form-item-control-input-content) {
    button {
      margin-right: 0;
      margin-left: 8px;
      box-shadow: 0 0 0 rgba(3, 38, 43, 0.42);
    }
  }
</style>
