<template>
  <div class="data-cards">
    <!-- 成交总额卡片 -->
    <div class="data-card">
      <div class="card-bg"></div>
      <div class="card-content">
        <div class="card-title">成交总额</div>
        <div class="card-value">
          <CountTo :start="0" :end="59645956" :duration="2000" :decimals="0" />
          <span class="card-unit">万元</span>
        </div>
        <div class="card-icon">
          <div class="icon-bg"></div>
        </div>
      </div>
    </div>

    <!-- 溢价总额卡片 -->
    <div class="data-card">
      <div class="card-bg"></div>
      <div class="card-content">
        <div class="card-title">溢价总额</div>
        <div class="card-value">
          <CountTo :start="0" :end="5956" :duration="2000" :decimals="0" />
          <span class="card-unit">万元</span>
        </div>
        <div class="card-icon">
          <div class="icon-bg"></div>
        </div>
      </div>
    </div>

    <!-- 总溢价率卡片 -->
    <div class="data-card">
      <div class="card-bg"></div>
      <div class="card-content">
        <div class="card-title">总溢价率</div>
        <div class="card-value">
          <CountTo :start="0" :end="95.0" :duration="2000" :decimals="2" />
          <span class="card-unit">%</span>
        </div>
        <div class="card-icon">
          <div class="icon-bg"></div>
        </div>
      </div>
    </div>

    <!-- 标的总量卡片 -->
    <div class="data-card">
      <div class="card-bg"></div>
      <div class="card-content">
        <div class="card-title">标的总量</div>
        <div class="card-value">
          <CountTo :start="0" :end="5956" :duration="2000" :decimals="0" />
          <span class="card-unit">个</span>
        </div>
        <div class="card-icon">
          <div class="icon-bg"></div>
        </div>
      </div>
    </div>

    <!-- 资产处置总量卡片 -->
    <div class="data-card">
      <div class="card-bg"></div>
      <div class="card-content">
        <div class="card-title">资产处置总量</div>
        <div class="card-value">
          <CountTo :start="0" :end="2956" :duration="2000" :decimals="0" />
          <span class="card-unit">个</span>
        </div>
        <div class="card-icon">
          <div class="icon-bg"></div>
        </div>
      </div>
    </div>

    <!-- 标的成交额卡片 -->
    <div class="data-card">
      <div class="card-bg"></div>
      <div class="card-content">
        <div class="card-title">标的成交额</div>
        <div class="card-value">
          <CountTo :start="0" :end="35956" :duration="2000" :decimals="0" />
          <span class="card-unit">万</span>
        </div>
        <div class="card-icon">
          <div class="icon-bg"></div>
        </div>
      </div>
    </div>

    <!-- 资产处置成交额卡片 -->
    <div class="data-card">
      <div class="card-bg"></div>
      <div class="card-content">
        <div class="card-title">资产处置成交额</div>
        <div class="card-value">
          <CountTo :start="0" :end="23689" :duration="2000" :decimals="0" />
          <span class="card-unit">万</span>
        </div>
        <div class="card-icon">
          <div class="icon-bg"></div>
        </div>
      </div>
    </div>

    <!-- 标的溢价率卡片 -->
    <div class="data-card">
      <div class="card-bg"></div>
      <div class="card-content">
        <div class="card-title">标的溢价率</div>
        <div class="card-value">
          <CountTo :start="0" :end="87.5" :duration="2000" :decimals="2" />
          <span class="card-unit">%</span>
        </div>
        <div class="card-icon">
          <div class="icon-bg"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import CountTo from './CountTo.vue';
</script>

<style lang="less" scoped>
  @import '../styles/fullScreen/DataCards.less';
</style>
