import { defHttp } from '/@/utils/http/axios';

enum Api {
  QueryAllReceive = '/receive/hgyReceive/queryAllReceive',
}

/**
 * 查询收件箱消息列表
 */
export const queryAllReceive = (params: {
  pageNo: number;
  pageSize: number;
  receiveUserId: string;
  keywords?: string;
}) => {
  return defHttp.post<{
    records: any[];
    total: number;
    size: number;
    current: number;
    pages: number;
  }>({
    url: Api.QueryAllReceive,
    data: params,
  });
};
