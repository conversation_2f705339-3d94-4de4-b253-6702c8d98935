import { defHttp } from '/@/utils/http/axios';

/**
 * 认证审核相关API接口
 */
enum Api {
  // 认证审核列表查询
  authReviewQueryPage = '/hgy/personalCenter/hgyEnterpriseAuth/workbench/authReviewQueryPage',
  // 个人认证详情
  getPersonalAuthDetail = '/hgy/personalCenter/hgyPersonalAuth/workbench/getPersonalAuthById',
  // 企业认证详情
  getEnterpriseAuthDetail = '/hgy/personalCenter/hgyEnterpriseAuth/workbench/getEnterpriseAuthById',
  // 个人认证审核
  reviewPersonalAuth = '/hgy/personalCenter/hgyPersonalAuth/platformReviewPersonalAuth',
  // 企业认证审核
  reviewEnterpriseAuth = '/hgy/personalCenter/hgyEnterpriseAuth/platformReviewEnterpriseAuth',
}

/**
 * 认证审核记录接口
 */
export interface CertificationAuditRecord {
  id: string; // 认证单ID
  enterpriseName: string; // 企业名称
  name: string; // 姓名
  phone: string; // 手机号
  review: number; // 审核状态（1-未审核，2-已通过，3-未通过）
  authType: string; // 认证类型（个人认证、企业认证）
  reviewTime: string; // 审核时间
  reviewUser: string; // 审核人
  submitTime: string; // 提交时间
  [key: string]: any;
}

/**
 * 分页查询参数
 */
export interface QueryPageParams {
  pageNo?: number;
  pageSize?: number;
  phone?: string; // 手机号
  name?: string; // 姓名
  enterpriseName?: string; // 企业名称
  review?: number; // 审核状态
  [key: string]: any;
}

/**
 * 分页查询结果
 */
export interface PageResult<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

/**
 * 审核参数接口
 */
export interface ReviewParams {
  id: string; // 认证ID
  review: number; // 审核状态（2-通过，3-拒绝）
  notes?: string; // 审核意见
}

/**
 * 认证审核列表查询
 * @param params 查询参数
 * @returns Promise<PageResult<CertificationAuditRecord>>
 */
export const queryPageAll = (params: QueryPageParams) => {
  return defHttp.post<PageResult<CertificationAuditRecord>>({
    url: Api.authReviewQueryPage,
    params,
  });
};

/**
 * 获取个人认证详情
 * @param id 认证ID
 * @returns Promise<any>
 */
export const getPersonalAuthDetail = (id: string) => {
  return defHttp.get<any>({
    url: Api.getPersonalAuthDetail,
    params: { id },
  });
};

/**
 * 获取企业认证详情
 * @param id 认证ID
 * @returns Promise<any>
 */
export const getEnterpriseAuthDetail = (id: string) => {
  return defHttp.get<any>({
    url: Api.getEnterpriseAuthDetail,
    params: { id },
  });
};

/**
 * 个人认证审核操作
 * @param params 审核参数
 * @returns Promise<any>
 */
export const reviewPersonalAuth = (params: ReviewParams) => {
  return defHttp.post<any>({
    url: Api.reviewPersonalAuth,
    data: params,
  });
};

/**
 * 企业认证审核操作
 * @param params 审核参数
 * @returns Promise<any>
 */
export const reviewEnterpriseAuth = (params: ReviewParams) => {
  return defHttp.post<any>({
    url: Api.reviewEnterpriseAuth,
    data: params,
  });
};

/**
 * 导出URL
 */
export const getExportUrl = Api.authReviewQueryPage + '/export';
