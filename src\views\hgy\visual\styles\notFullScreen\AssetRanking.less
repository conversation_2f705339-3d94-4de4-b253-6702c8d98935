.asset-ranking {
  position: relative;
  width: 577px;
  height: 578px;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: row-reverse;
}

.ranking-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 577px;
  height: 578px;
  background: url('https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/right-bg_1754987518249.png') no-repeat center center;
  background-size: cover;
  z-index: 1;
}

.ranking-content {
  position: relative;
  width: 78%;
  height: 100%;
  padding: 20px; /* 由 px->vw 插件转换为相对单位 */
  z-index: 2;
  display: flex;
  flex-direction: column;
  // 靠右显示
}

/* 总标题样式 */
.main-title {
  color: #ffffff;
  font-size: 20px;
  font-family: 'PingFang Bold';
  text-align: center;
  position: relative;
  top: -5px;
}

.ranking-section {
  flex: 1;
}

/* 板块标题容器 */
.section-header {
  display: flex;
  align-items: center;
  margin: 10px 0;
  gap: 10px;
  width: 106%;
  // 靠左
  margin-left: -5%;
}

.width-limit {
  width: 95%;
  margin-left: 6%;
}

/* 板块标题两侧分割线 - 从两侧向中间渐变 */
.section-divider {
  flex: 1;
  height: 1px;

  &.left {
    background: linear-gradient(90deg, rgba(88, 225, 255, 0.6) 0%, rgba(88, 225, 255, 0.2) 70%, transparent 100%);
  }

  &.right {
    background: linear-gradient(90deg, transparent 0%, rgba(88, 225, 255, 0.2) 30%, rgba(88, 225, 255, 0.6) 100%);
  }
}

.section-title {
  color: #58e1ff;
  font-size: 16px;
  font-weight: bold;
  text-align: center;
  text-shadow: 0 0 10px rgba(18, 230, 219, 0.5);
  white-space: nowrap;
  flex-shrink: 0;
}

.ranking-list {
  display: flex;
  flex-direction: column;
  width: 380px;
  margin-left: auto; /* 右对齐 */
}

.ranking-item {
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.3s ease;
  margin: 2px; /* 由 px->vw 插件转换 */

  &:hover {
    // transform: translateX(-5px);
    transform: scale(1.02);
  }
}

.medal-icon {
  position: relative;
  width: 32px;
  height: 20px;
  flex-shrink: 0;

  .medal-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    background-image: url('@/assets/visual/medal-regular.png');
  }

  .rank-number {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #004c66 !important; /* 使用!important确保优先级 */
    font-size: 11px;
    z-index: 2;
    font-family: 'DIN Bold';
  }

  &.rank-1 .medal-bg {
    background-image: url('@/assets/visual/medal-gold.png');
  }

  &.rank-2 .medal-bg {
    background-image: url('@/assets/visual/medal-silver.png');
  }

  &.rank-3 .medal-bg {
    background-image: url('@/assets/visual/medal-cuprum.png');
  }
}

.project-name {
  color: #ffffff;
  font-size: 14px; /* 字体将被插件转成 vw */
  width: 120px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.progress-container {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: #0b6d73;
  border-radius: 4px;
  overflow: visible; /* 改为visible让菱形角能够显示 */
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #044d60 0%, #58e1ff 100%);
  border-radius: 4px;
  transition: width 2s ease;
  position: relative;
}

.progress-diamond {
  position: absolute;
  top: 50%;
  right: -4.95px; /* 菱形宽度的一半，让菱形中心对齐进度条右端 */
  width: 9.9px;
  height: 9.9px;
  background: #eafffe;
  transform: translateY(-50%) rotate(45deg); /* 垂直居中并旋转45度形成菱形 */
  border-radius: 2px; /* 稍微圆润的角 */
}

.progress-value {
  color: #fff;
  font-size: 16px;
  font-family: 'DIN Bold';
  min-width: 60px;
  text-align: right;
  text-shadow: 0 0 8px rgba(18, 230, 219, 0.5);

  .animated-number {
    display: inline-block;
    transition: all 0.3s ease;
  }

  .progress-unit {
    font-family: 'PingFang Medium';
    font-size: 12px;
    margin-left: 5px;
  }
}
