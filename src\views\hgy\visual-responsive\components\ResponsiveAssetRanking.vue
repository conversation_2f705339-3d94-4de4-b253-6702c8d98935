<template>
  <div class="responsive-asset-ranking">
    <!-- 背景 -->
    <div class="ranking-bg"></div>
    
    <!-- 内容 -->
    <div class="ranking-content">
      <div class="ranking-header">
        <h3 class="ranking-title">资产处置数据排名</h3>
        <div class="ranking-tabs">
          <div 
            class="tab-item" 
            :class="{ active: activeTab === 'amount' }"
            @click="activeTab = 'amount'"
          >
            处置额
          </div>
          <div 
            class="tab-item" 
            :class="{ active: activeTab === 'rate' }"
            @click="activeTab = 'rate'"
          >
            溢价率
          </div>
        </div>
      </div>
      
      <div class="ranking-list" ref="rankingListRef">
        <div 
          v-for="(item, index) in currentRankingData" 
          :key="item.id"
          class="rank-item"
          :class="{ 'top-rank': index < 3 }"
          :style="{ animationDelay: `${index * 0.1}s` }"
        >
          <div class="rank-number">
            <span v-if="index < 3" class="medal">{{ index + 1 }}</span>
            <span v-else class="number">{{ index + 1 }}</span>
          </div>
          <div class="rank-info">
            <div class="rank-name" :title="item.name">{{ item.name }}</div>
            <div class="rank-type">{{ item.type }}</div>
          </div>
          <div class="rank-value">
            <span class="value">{{ formatValue(item.value, activeTab) }}</span>
            <span class="unit">{{ getUnit(activeTab) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, inject } from 'vue';

// 注入数据服务
const dataService = inject('dataService') as any;

// 当前激活的标签
const activeTab = ref<'amount' | 'rate'>('amount');

// 排名列表引用
const rankingListRef = ref<HTMLElement>();

// 排名数据
const rankingData = ref({
  amount: [] as Array<{
    id: string;
    name: string;
    type: string;
    value: number;
  }>,
  rate: [] as Array<{
    id: string;
    name: string;
    type: string;
    value: number;
  }>,
});

// 当前显示的排名数据
const currentRankingData = computed(() => {
  return rankingData.value[activeTab.value];
});

// 格式化数值
const formatValue = (value: number, type: 'amount' | 'rate'): string => {
  if (type === 'amount') {
    return (value / 10000).toFixed(2);
  } else {
    return value.toFixed(2);
  }
};

// 获取单位
const getUnit = (type: 'amount' | 'rate'): string => {
  return type === 'amount' ? '万元' : '%';
};

// 加载数据
const loadData = async () => {
  try {
    // 模拟数据加载
    await new Promise(resolve => setTimeout(resolve, 600));
    
    rankingData.value = {
      amount: [
        { id: '1', name: '中原银行不良资产包', type: '金融资产', value: 89650000 },
        { id: '2', name: '郑州房地产项目', type: '房地产', value: 76540000 },
        { id: '3', name: '洛阳钢铁厂设备', type: '工业设备', value: 65430000 },
        { id: '4', name: '开封土地使用权', type: '土地权益', value: 54320000 },
        { id: '5', name: '新乡化工企业股权', type: '股权资产', value: 43210000 },
        { id: '6', name: '焦作煤矿开采权', type: '采矿权', value: 38760000 },
        { id: '7', name: '安阳机械制造厂', type: '制造业', value: 32450000 },
        { id: '8', name: '濮阳物流园区', type: '物流资产', value: 28900000 },
        { id: '9', name: '许昌农业合作社', type: '农业资产', value: 24560000 },
        { id: '10', name: '漯河食品加工厂', type: '食品工业', value: 21340000 },
      ],
      rate: [
        { id: '1', name: '中原银行不良资产包', type: '金融资产', value: 52.8 },
        { id: '2', name: '开封土地使用权', type: '土地权益', value: 48.9 },
        { id: '3', name: '郑州房地产项目', type: '房地产', value: 45.6 },
        { id: '4', name: '新乡化工企业股权', type: '股权资产', value: 42.3 },
        { id: '5', name: '洛阳钢铁厂设备', type: '工业设备', value: 38.7 },
        { id: '6', name: '焦作煤矿开采权', type: '采矿权', value: 35.4 },
        { id: '7', name: '安阳机械制造厂', type: '制造业', value: 32.1 },
        { id: '8', name: '濮阳物流园区', type: '物流资产', value: 28.9 },
        { id: '9', name: '许昌农业合作社', type: '农业资产', value: 25.6 },
        { id: '10', name: '漯河食品加工厂', type: '食品工业', value: 22.3 },
      ],
    };
  } catch (error) {
    console.error('加载资产排名数据失败:', error);
  }
};

onMounted(() => {
  loadData();
});
</script>

<style lang="less" scoped>
@import '../styles/responsive-variables.less';
@import '../styles/responsive-mixins.less';

.responsive-asset-ranking {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 500px;
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.ranking-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/right-bg_1754987518249.png') no-repeat center center;
  background-size: cover;
  opacity: 0.8;
  z-index: 1;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-bg);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
  }
}

.ranking-content {
  position: relative;
  z-index: 2;
  height: 100%;
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  
  .respond-to(xs) {
    .content() {
      padding: var(--spacing-md);
    }
  }
}

.ranking-header {
  margin-bottom: var(--spacing-lg);
  
  .respond-to(xs) {
    .content() {
      margin-bottom: var(--spacing-md);
    }
  }
}

.ranking-title {
  .responsive-font-size(var(--font-lg));
  color: var(--text-light);
  font-weight: bold;
  text-align: center;
  margin: 0 0 var(--spacing-md) 0;
  .text-glow(var(--primary-color), 10px, 0.5);
}

.ranking-tabs {
  display: flex;
  justify-content: center;
  gap: var(--spacing-sm);
}

.tab-item {
  .responsive-font-size(var(--font-sm));
  color: var(--text-secondary);
  padding: var(--spacing-xs) var(--spacing-md);
  background: var(--bg-dark-40);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--duration-normal) ease;
  
  &:hover {
    color: var(--text-light);
    border-color: var(--primary-color-40);
  }
  
  &.active {
    color: var(--primary-color);
    background: var(--primary-color-10);
    border-color: var(--primary-color);
    .glow-effect(var(--primary-color), 8px, 0.3);
  }
  
  .respond-to(xs) {
    .content() {
      padding: var(--spacing-xs) var(--spacing-sm);
    }
  }
}

.ranking-list {
  flex: 1;
  overflow-y: auto;
  .responsive-ranking-list();
  
  &::-webkit-scrollbar {
    width: 4px;
  }
  
  &::-webkit-scrollbar-track {
    background: var(--bg-dark-20);
    border-radius: 2px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 2px;
    
    &:hover {
      background: var(--primary-color-80);
    }
  }
}

.rank-item {
  .responsive-animation(slideInRight, 0.6s, ease-out);
  
  &.top-rank {
    .rank-number .medal {
      width: 28px;
      height: 28px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      color: var(--text-light);
      font-weight: bold;
      .text-glow(var(--text-light), 6px, 0.8);
    }
    
    &:nth-child(1) .rank-number .medal {
      background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
      .glow-effect(#ffd700, 8px, 0.6);
    }
    
    &:nth-child(2) .rank-number .medal {
      background: linear-gradient(135deg, #c0c0c0 0%, #e8e8e8 100%);
      .glow-effect(#c0c0c0, 8px, 0.6);
    }
    
    &:nth-child(3) .rank-number .medal {
      background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%);
      .glow-effect(#cd7f32, 8px, 0.6);
    }
  }
  
  .rank-number .number {
    color: var(--text-secondary);
    font-weight: bold;
  }
}

.rank-info {
  flex: 1;
  min-width: 0;
}

.rank-name {
  .responsive-font-size(var(--font-sm));
  color: var(--text-light);
  font-weight: 500;
  margin-bottom: 2px;
}

.rank-type {
  .responsive-font-size(var(--font-xs));
  color: var(--text-muted);
}

.rank-value {
  text-align: right;
  
  .value {
    .responsive-font-size(var(--font-md));
    color: var(--primary-color);
    font-weight: bold;
    .text-glow(var(--primary-color), 6px, 0.4);
  }
  
  .unit {
    .responsive-font-size(var(--font-xs));
    color: var(--text-secondary);
    margin-left: 2px;
  }
}

// 动画定义
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 响应式优化
@media (max-height: 600px) {
  .responsive-asset-ranking {
    min-height: 400px;
  }
  
  .ranking-content {
    padding: var(--spacing-md);
  }
  
  .rank-item {
    height: calc(var(--rank-item-height) * 0.8);
  }
}</style>
