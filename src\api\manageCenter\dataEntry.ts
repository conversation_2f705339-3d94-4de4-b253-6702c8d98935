import { defHttp } from '/@/utils/http/axios';

// 数据补录记录接口
export interface DataEntryRecord {
  id: string;
  companyName: string;
  entrustOrderId: string;
  dataType: string;
  projectName: string;
  disposalPrice: number;
  isFlowShot: number; // 0-否 1-是
  transactionAmount: number;
  premiumAmount: number;
  premiumRate: number;
  operatorName: string;
  operateTime: string;
}

// 查询参数接口
export interface DataEntryQueryParams {
  companyName?: string;
  entrustOrderId?: string;
  pageNo?: number;
  pageSize?: number;
}

// 获取数据补录列表
export const getDataEntryList = (params: DataEntryQueryParams) => {
  return defHttp.get<{
    records: DataEntryRecord[];
    total: number;
  }>({
    url: '/hgy/visual/hgyVisualData/list',
    params,
  });
};

// 导出数据补录列表
export const exportDataEntry = '/hgy/visual/hgyVisualData/exportXls';

// 导出数据补录模板
export const exportDataEntryTemplate = '/hgy/visual/hgyVisualData/exportTemplateXls';

// 导入数据补录
export const importDataEntry = (params: any) => {
  return defHttp.post({
    url: '/hgy/visual/hgyVisualData/importExcel',
    params,
  });
};
