/**
 * 全屏管理 Composable
 * 提供全屏功能的统一管理
 */
import { ref, onMounted, onUnmounted } from 'vue';

export function useFullscreen() {
  const isFullscreen = ref(false);
  const isSupported = ref(false);

  /**
   * 检查浏览器是否支持全屏API
   */
  const checkSupport = () => {
    const element = document.documentElement;
    isSupported.value = !!(
      element.requestFullscreen ||
      (element as any).webkitRequestFullscreen ||
      (element as any).mozRequestFullScreen ||
      (element as any).msRequestFullscreen
    );
  };

  /**
   * 进入全屏
   */
  const enterFullscreen = async (element?: HTMLElement): Promise<boolean> => {
    if (!isSupported.value) {
      console.warn('当前浏览器不支持全屏API');
      return false;
    }

    const targetElement = element || document.documentElement;

    try {
      if (targetElement.requestFullscreen) {
        await targetElement.requestFullscreen();
      } else if ((targetElement as any).webkitRequestFullscreen) {
        await (targetElement as any).webkitRequestFullscreen();
      } else if ((targetElement as any).mozRequestFullScreen) {
        await (targetElement as any).mozRequestFullScreen();
      } else if ((targetElement as any).msRequestFullscreen) {
        await (targetElement as any).msRequestFullscreen();
      }
      return true;
    } catch (error) {
      console.error('进入全屏失败:', error);
      return false;
    }
  };

  /**
   * 退出全屏
   */
  const exitFullscreen = async (): Promise<boolean> => {
    if (!isSupported.value) {
      console.warn('当前浏览器不支持全屏API');
      return false;
    }

    try {
      if (document.exitFullscreen) {
        await document.exitFullscreen();
      } else if ((document as any).webkitExitFullscreen) {
        await (document as any).webkitExitFullscreen();
      } else if ((document as any).mozCancelFullScreen) {
        await (document as any).mozCancelFullScreen();
      } else if ((document as any).msExitFullscreen) {
        await (document as any).msExitFullscreen();
      }
      return true;
    } catch (error) {
      console.error('退出全屏失败:', error);
      return false;
    }
  };

  /**
   * 切换全屏状态
   */
  const toggleFullscreen = async (element?: HTMLElement): Promise<boolean> => {
    if (isFullscreen.value) {
      return await exitFullscreen();
    } else {
      return await enterFullscreen(element);
    }
  };

  /**
   * 检查当前是否处于全屏状态
   */
  const checkFullscreenStatus = (): boolean => {
    return !!(
      document.fullscreenElement ||
      (document as any).webkitFullscreenElement ||
      (document as any).mozFullScreenElement ||
      (document as any).msFullscreenElement
    );
  };

  /**
   * 更新全屏状态
   */
  const updateFullscreenStatus = () => {
    isFullscreen.value = checkFullscreenStatus();
  };

  /**
   * 全屏状态变化事件处理
   */
  const handleFullscreenChange = () => {
    updateFullscreenStatus();
  };

  /**
   * 全屏错误事件处理
   */
  const handleFullscreenError = (event: Event) => {
    console.error('全屏操作出错:', event);
    isFullscreen.value = false;
  };

  /**
   * 键盘事件处理
   */
  const handleKeydown = (event: KeyboardEvent) => {
    // ESC键退出全屏
    if (event.key === 'Escape' && isFullscreen.value) {
      exitFullscreen();
    }
    // F11键切换全屏
    else if (event.key === 'F11') {
      event.preventDefault();
      toggleFullscreen();
    }
  };

  /**
   * 添加事件监听器
   */
  const addEventListeners = () => {
    // 全屏状态变化事件
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);

    // 全屏错误事件
    document.addEventListener('fullscreenerror', handleFullscreenError);
    document.addEventListener('webkitfullscreenerror', handleFullscreenError);
    document.addEventListener('mozfullscreenerror', handleFullscreenError);
    document.addEventListener('MSFullscreenError', handleFullscreenError);

    // 键盘事件
    document.addEventListener('keydown', handleKeydown);
  };

  /**
   * 移除事件监听器
   */
  const removeEventListeners = () => {
    // 全屏状态变化事件
    document.removeEventListener('fullscreenchange', handleFullscreenChange);
    document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
    document.removeEventListener('MSFullscreenChange', handleFullscreenChange);

    // 全屏错误事件
    document.removeEventListener('fullscreenerror', handleFullscreenError);
    document.removeEventListener('webkitfullscreenerror', handleFullscreenError);
    document.removeEventListener('mozfullscreenerror', handleFullscreenError);
    document.removeEventListener('MSFullscreenError', handleFullscreenError);

    // 键盘事件
    document.removeEventListener('keydown', handleKeydown);
  };

  /**
   * 获取全屏元素
   */
  const getFullscreenElement = (): Element | null => {
    return (
      document.fullscreenElement ||
      (document as any).webkitFullscreenElement ||
      (document as any).mozFullScreenElement ||
      (document as any).msFullscreenElement ||
      null
    );
  };

  /**
   * 请求全屏权限（某些浏览器需要）
   */
  const requestFullscreenPermission = async (): Promise<boolean> => {
    try {
      // 某些浏览器可能需要用户手势才能进入全屏
      // 这里可以显示一个按钮让用户点击
      return true;
    } catch (error) {
      console.error('请求全屏权限失败:', error);
      return false;
    }
  };

  // 组件挂载时初始化
  onMounted(() => {
    checkSupport();
    updateFullscreenStatus();
    addEventListeners();
  });

  // 组件卸载时清理
  onUnmounted(() => {
    removeEventListeners();
  });

  return {
    // 响应式数据
    isFullscreen,
    isSupported,

    // 方法
    enterFullscreen,
    exitFullscreen,
    toggleFullscreen,
    checkFullscreenStatus,
    getFullscreenElement,
    requestFullscreenPermission,

    // 事件处理器（可选暴露）
    handleFullscreenChange,
    handleFullscreenError,
    handleKeydown,
  };
}
